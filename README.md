# Image Transcriber

A modern GUI application for image transcription using multiple OCR engines with drawing capabilities for precise text extraction.

## ✨ Features

- **🎨 Modern GUI**: Resizable panels with clean aesthetics using CustomTkinter
- **📁 Project Management**: Create and manage transcription projects with persistent state
- **🖼️ Image Canvas**: Interactive canvas for drawing crop regions on images
- **🔍 Multiple OCR Engines**: Support for Tesseract, EasyOCR, and extensible architecture
- **✏️ Editable Transcriptions**: Edit, correct, and save OCR results with confidence scores
- **💾 Persistent State**: Save drawings, settings, and progress for resumability
- **📊 Comprehensive Logging**: Track all operations and errors with detailed logs
- **🛡️ Error Handling**: Robust crash prevention and recovery system
- **🧪 Testing Suite**: Comprehensive tests for reliability and stability

## 🖥️ GUI Layout

The application features a modern, resizable panel layout:

- **Left Panel**: Project file browser and management tools
- **Main Panel**: Interactive image canvas with drawing tools
- **Right Panel**: OCR settings, transcription results, and editing tools
- **Bottom Panel**: Application logs and status information

## 🚀 Installation

### Prerequisites

**Important**: This application requires tkinter, which may not be installed by default on some systems.

#### Install tkinter (if missing):

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install python3-tk
```

**CentOS/RHEL/Fedora:**
```bash
# CentOS/RHEL
sudo yum install python3-tkinter
# Fedora
sudo dnf install python3-tkinter
```

**macOS:**
```bash
# If using Homebrew Python
brew install python-tk
```

**Windows:** tkinter should be included with Python by default.

### Application Setup

1. **Clone the repository:**

```bash
git clone <repository-url>
cd ImageTranscriber
```

2. **Create a virtual environment:**

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Python dependencies:**

```bash
pip install -r requirements.txt
```

4. **Install Tesseract OCR:**

- **macOS**: `brew install tesseract`
- **Ubuntu/Debian**: `sudo apt-get install tesseract-ocr`
- **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)

5. **Verify tkinter installation:**

```bash
python3 -c "import tkinter; print('tkinter is available')"
```

## 🎯 Usage

### Running the Application

```bash
python main.py
```

### Basic Workflow

1. **Create or Open a Project**
   - Click "New Project" to create a new transcription project
   - Or click "Open Project" to continue working on an existing project

2. **Import Images**
   - Use "Import Images" to add image files to your project
   - Supported formats: JPEG, PNG, BMP, TIFF, WebP

3. **Select and View Images**
   - Click on images in the file browser to select them
   - Double-click to open them in the main canvas

4. **Draw Crop Regions**
   - Click "Draw Mode" to enable drawing
   - Draw rectangles around text areas you want to transcribe
   - Multiple regions can be drawn on each image

5. **Configure OCR Settings**
   - Select your preferred OCR engine (Tesseract, EasyOCR)
   - Choose the appropriate language
   - Adjust confidence thresholds and other settings

6. **Generate Transcriptions**
   - Click "Generate Transcription" or double-click on drawn regions
   - Review and edit the extracted text
   - Save your corrections

7. **Manage Results**
   - View all transcriptions in the right panel
   - Edit text directly in the editor
   - Delete unwanted transcriptions
   - All changes are automatically saved

### Troubleshooting

**If you get "No module named '_tkinter'" error:**

Follow the tkinter installation instructions above, then restart your terminal and try again.

**If OCR engines are not working:**

- Ensure Tesseract is properly installed and in your PATH
- Check the logs panel for detailed error messages
- Verify image files are in supported formats

## 📁 Project Structure

```text
ImageTranscriber/
├── main.py                 # Application entry point
├── run_tests.py           # Test runner
├── requirements.txt       # Python dependencies
├── README.md              # This file
├── src/                   # Source code
│   ├── app.py             # Main application controller
│   ├── config/            # Configuration management
│   │   └── settings.py    # Application settings
│   ├── gui/               # GUI components
│   │   ├── main_window.py # Main application window
│   │   ├── file_browser.py# File management panel
│   │   ├── image_canvas.py# Image display and drawing
│   │   ├── settings_panel.py # OCR settings panel
│   │   └── log_panel.py   # Logging panel
│   ├── models/            # Data models
│   │   └── project.py     # Project, image, and transcription models
│   ├── ocr/               # OCR engine integrations
│   │   ├── base.py        # Base OCR engine interface
│   │   ├── tesseract_engine.py # Tesseract integration
│   │   ├── easyocr_engine.py   # EasyOCR integration
│   │   └── manager.py     # OCR engine manager
│   └── utils/             # Utility functions
│       ├── logger.py      # Logging configuration
│       ├── image_utils.py # Image processing utilities
│       ├── error_handler.py # Error handling utilities
│       └── crash_handler.py # Crash prevention and recovery
├── tests/                 # Test files
│   ├── test_models.py     # Model tests
│   └── test_ocr.py        # OCR functionality tests
├── projects/              # User projects storage (created at runtime)
├── logs/                  # Application logs (created at runtime)
└── temp/                  # Temporary files (created at runtime)
```

## 🧪 Testing

Run the test suite to ensure everything is working correctly:

```bash
python run_tests.py
```

Or run tests directly with pytest:

```bash
pytest tests/ -v
```

For coverage reports:

```bash
pytest tests/ --cov=src --cov-report=html
```

## 🔧 Development

### Adding New OCR Engines

1. Create a new engine class inheriting from `BaseOCREngine`
2. Implement required methods: `extract_text`, `get_supported_languages`, etc.
3. Register the engine in `OCRManager`

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📋 Requirements

- Python 3.8+
- tkinter (GUI framework)
- CustomTkinter (modern GUI components)
- Pillow (image processing)
- Tesseract OCR (optional but recommended)
- EasyOCR (optional)
- See `requirements.txt` for complete list

## 🐛 Known Issues

- Large images may take time to load and process
- OCR accuracy depends on image quality and text clarity
- Some OCR engines may require additional language packs

## 📞 Support

If you encounter issues:

1. Check the logs panel in the application
2. Review the log files in the `logs/` directory
3. Ensure all dependencies are properly installed
4. Verify tkinter is available on your system

## 🏆 Features Implemented

✅ Modern GUI with resizable panels
✅ Project creation and management
✅ Image import and file browser
✅ Interactive drawing canvas
✅ Multiple OCR engine support
✅ Transcription editing and management
✅ Persistent state and auto-save
✅ Comprehensive error handling
✅ Detailed logging system
✅ Crash prevention and recovery
✅ Complete test suite
✅ Professional documentation

## 📄 License

MIT License - see LICENSE file for details
