# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

official_categories = {
    "PP-YOLOE-L_human": [{"name": "pedestrian", "id": 0}],
    "PP-YOLOE-S_human": [{"name": "pedestrian", "id": 0}],
    "PP-YOLOE-S_vehicle": [{"name": "vehicle", "id": 0}],
    "PP-YOLOE-L_vehicle": [{"name": "vehicle", "id": 0}],
    "PP-ShiTuV2_det": [{"name": "mainbody", "id": 0}],
    "PicoDet_layout_1x": [
        {"name": "Text", "id": 0},
        {"name": "Title", "id": 1},
        {"name": "List", "id": 2},
        {"name": "Table", "id": 3},
        {"name": "Figure", "id": 4},
    ],
    "PicoDet_layout_1x_table": [
        {"name": "Table", "id": 0},
    ],
    "PicoDet-S_layout_3cls": [
        {"name": "image", "id": 0},
        {"name": "table", "id": 1},
        {"name": "seal", "id": 2},
    ],
    "PicoDet-S_layout_17cls": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
    ],
    "PicoDet-L_layout_3cls": [
        {"name": "image", "id": 0},
        {"name": "table", "id": 1},
        {"name": "seal", "id": 2},
    ],
    "PicoDet-L_layout_17cls": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
    ],
    "RT-DETR-H_layout_3cls": [
        {"name": "image", "id": 0},
        {"name": "table", "id": 1},
        {"name": "seal", "id": 2},
    ],
    "RT-DETR-H_layout_17cls": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
    ],
    "PP-YOLOE_plus_SOD-S": [
        {"name": "pedestrian", "id": 0},
        {"name": "people", "id": 1},
        {"name": "bicycle", "id": 2},
        {"name": "car", "id": 3},
        {"name": "van", "id": 4},
        {"name": "truck", "id": 5},
        {"name": "tricycle", "id": 6},
        {"name": "awning-tricycle", "id": 7},
        {"name": "bus", "id": 8},
        {"name": "motorcycle", "id": 9},
    ],
    "PP-YOLOE_plus_SOD-L": [
        {"name": "pedestrian", "id": 0},
        {"name": "people", "id": 1},
        {"name": "bicycle", "id": 2},
        {"name": "car", "id": 3},
        {"name": "van", "id": 4},
        {"name": "truck", "id": 5},
        {"name": "tricycle", "id": 6},
        {"name": "awning-tricycle", "id": 7},
        {"name": "bus", "id": 8},
        {"name": "motorcycle", "id": 9},
    ],
    "PP-YOLOE_plus_SOD-largesize-L": [
        {"name": "pedestrian", "id": 0},
        {"name": "people", "id": 1},
        {"name": "bicycle", "id": 2},
        {"name": "car", "id": 3},
        {"name": "van", "id": 4},
        {"name": "truck", "id": 5},
        {"name": "tricycle", "id": 6},
        {"name": "awning-tricycle", "id": 7},
        {"name": "bus", "id": 8},
        {"name": "motorcycle", "id": 9},
    ],
    "BlazeFace": [{"name": "face", "id": 0}],
    "BlazeFace-FPN-SSH": [{"name": "face", "id": 0}],
    "PicoDet_LCNet_x2_5_face": [{"name": "face", "id": 0}],
    "PP-YOLOE_plus-S_face": [{"name": "face", "id": 0}],
    "RT-DETR-L_wired_table_cell_det": [{"name": "cell", "id": 0}],
    "RT-DETR-L_wireless_table_cell_det": [{"name": "cell", "id": 0}],
    "PP-DocLayout-L": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
        {"name": "chart_title", "id": 17},
        {"name": "chart", "id": 18},
        {"name": "formula_number", "id": 19},
        {"name": "header_image", "id": 20},
        {"name": "footer_image", "id": 21},
        {"name": "aside_text", "id": 22},
    ],
    "PP-DocLayout-M": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
        {"name": "chart_title", "id": 17},
        {"name": "chart", "id": 18},
        {"name": "formula_number", "id": 19},
        {"name": "header_image", "id": 20},
        {"name": "footer_image", "id": 21},
        {"name": "aside_text", "id": 22},
    ],
    "PP-DocLayout-S": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "table_title", "id": 9},
        {"name": "reference", "id": 10},
        {"name": "doc_title", "id": 11},
        {"name": "footnote", "id": 12},
        {"name": "header", "id": 13},
        {"name": "algorithm", "id": 14},
        {"name": "footer", "id": 15},
        {"name": "seal", "id": 16},
        {"name": "chart_title", "id": 17},
        {"name": "chart", "id": 18},
        {"name": "formula_number", "id": 19},
        {"name": "header_image", "id": 20},
        {"name": "footer_image", "id": 21},
        {"name": "aside_text", "id": 22},
    ],
    "PP-DocLayout_plus-L": [
        {"name": "paragraph_title", "id": 0},
        {"name": "image", "id": 1},
        {"name": "text", "id": 2},
        {"name": "number", "id": 3},
        {"name": "abstract", "id": 4},
        {"name": "content", "id": 5},
        {"name": "figure_title", "id": 6},
        {"name": "formula", "id": 7},
        {"name": "table", "id": 8},
        {"name": "reference", "id": 9},
        {"name": "doc_title", "id": 10},
        {"name": "footnote", "id": 11},
        {"name": "header", "id": 12},
        {"name": "algorithm", "id": 13},
        {"name": "footer", "id": 14},
        {"name": "seal", "id": 15},
        {"name": "chart", "id": 16},
        {"name": "formula_number", "id": 17},
        {"name": "aside_text", "id": 18},
        {"name": "reference_content", "id": 19},
    ],
    "PP-DocBlockLayout": [
        {"name": "Region", "id": 0},
    ],
}
