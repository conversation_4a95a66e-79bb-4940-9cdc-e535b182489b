# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


MODELS = [
    "Deeplabv3_Plus-R101",
    "Deeplabv3_Plus-R50",
    "Deeplabv3-R101",
    "Deeplabv3-R50",
    "OCRNet_HRNet-W48",
    "OCRNet_HRNet-W18",
    "PP-LiteSeg-T",
    "PP-LiteSeg-B",
    "SegFormer-B0",
    "SegFormer-B1",
    "SegFormer-B2",
    "SegFormer-B3",
    "SegFormer-B4",
    "SegFormer-B5",
    "SeaFormer_base",
    "SeaFormer_tiny",
    "SeaFormer_small",
    "SeaFormer_large",
    "Mask<PERSON>ormer_tiny",
    "Mask<PERSON>ormer_small",
]
