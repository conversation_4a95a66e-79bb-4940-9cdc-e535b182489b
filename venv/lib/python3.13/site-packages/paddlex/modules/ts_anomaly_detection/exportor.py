# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from pathlib import Path

from ...utils import logging
from ..base import BaseExportor
from .model_list import MODELS


class TSADExportor(BaseExportor):
    """Image Classification Model Exportor"""

    entities = MODELS

    def get_config_path(self, weight_path):
        """
        get config path

        Args:
            weight_path (str): The path to the weight

        Returns:
            config_path (str): The path to the config

        """
        config_path = Path(self.export_config.weight_path).parent.parent / "config.yaml"
        if not config_path.exists():
            logging.warning(
                f"The config file(`{config_path}`) related to weight file(`{weight_path}`) is not exist, use default instead."
            )
            config_path = None
        return config_path
