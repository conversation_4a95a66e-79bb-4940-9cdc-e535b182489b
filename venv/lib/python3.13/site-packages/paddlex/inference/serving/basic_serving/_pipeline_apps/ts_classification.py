# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Any

from .....utils.deps import function_requires_deps, is_dep_available
from ...infra import utils as serving_utils
from ...infra.config import AppConfig
from ...infra.models import AIStudioResultResponse
from ...schemas.ts_classification import INFER_ENDPOINT, InferRequest, InferResult
from .._app import create_app, primary_operation

if is_dep_available("fastapi"):
    from fastapi import Fast<PERSON>I


@function_requires_deps("fastapi")
def create_pipeline_app(pipeline: Any, app_config: AppConfig) -> "FastAPI":
    app, ctx = create_app(
        pipeline=pipeline, app_config=app_config, app_aiohttp_session=True
    )

    @primary_operation(
        app,
        INFER_ENDPOINT,
        "infer",
    )
    async def _infer(request: InferRequest) -> AIStudioResultResponse[InferResult]:
        pipeline = ctx.pipeline
        aiohttp_session = ctx.aiohttp_session
        visualize_enabled = (
            request.visualize if request.visualize is not None else ctx.config.visualize
        )
        file_bytes = await serving_utils.get_raw_bytes_async(
            request.csv, aiohttp_session
        )
        df = serving_utils.csv_bytes_to_data_frame(file_bytes)

        result = (await pipeline.infer(df))[0]

        label = str(result["classification"].at[0, "classid"])
        score = float(result["classification"].at[0, "score"])
        if visualize_enabled:
            output_image = serving_utils.base64_encode(
                serving_utils.image_to_bytes(result.img["res"].convert("RGB"))
            )
        else:
            output_image = None

        return AIStudioResultResponse[InferResult](
            logId=serving_utils.generate_log_id(),
            result=InferResult(label=label, score=score, image=output_image),
        )

    return app
