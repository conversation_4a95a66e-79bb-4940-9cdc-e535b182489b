# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

TRT_BLOCKLIST = [
    "TimesNet_cls",
    "TimesNet",
    "TimesNet_ad",
    "MaskRCNN-ResNet50-FPN",
    "FasterRCNN-ResNeXt101-vd-FPN",
    "Cascade-FasterRCNN-ResNet50-FPN",
    "MaskRCNN-ResNet101-vd-FPN",
    "FasterRCNN-ResNet50",
    "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN",
    "FasterRCNN-ResNet50-FPN",
    "FasterRCNN-ResNet101",
    "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN",
    "MaskRCNN-ResNeXt101-vd-FPN",
    "MaskRCNN-ResNet50",
    "FasterRCNN-ResNet50-vd-FPN",
    "FasterRCNN-Swin-Tiny-FPN",
    "FasterRCNN-ResNet34-FPN",
    "MaskRCNN-ResNet101-FPN",
    "FasterRCNN-ResNet50-vd-SSLDv2-FPN",
    "FasterRCNN-ResNet101-FPN",
    "MaskRCNN-ResNet50-vd-FPN",
    "Cascade-MaskRCNN-ResNet50-FPN",
    "MaskRCNN-ResNet50-vd-FPN",
    "Cascade-MaskRCNN-ResNet50-FPN",
    "SOLOv2",
    "CenterNet-DLA-34",
    "CenterNet-ResNet50",
]
