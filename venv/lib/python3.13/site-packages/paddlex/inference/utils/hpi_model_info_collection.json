{"cpu_x64": {"paddle30": {"PP-LCNet_x1_0_doc_ori": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "MobileFaceNet": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_face": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "CLIP_vit_large_patch14_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "ConvNeXt_base_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_base_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-M": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x2_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_35": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_35": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_hybrid_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_hybrid_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "PP-HGNet_base": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNet_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNet_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNetV2_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNetV2_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_25": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x0_35": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x0_5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x2_5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ResNet101": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet101_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet152": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet152_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet18": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet18_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet200_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet34": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet34_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["paddle_mkldnn", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["paddle_mkldnn", "paddle"], "PP-ShiTuV2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_448_ML": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B0_ML": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B6_ML": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ResNet50_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-DocLayout-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-H_layout_17cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-H_layout_3cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-H": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R18": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R50": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-X": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x1_0_pedestrian_attribute": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_server_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W18": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W48": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime"], "SeaFormer_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wired_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wireless_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x1_0_table_cls": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "PP-OCRv4_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "arabic_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ch_SVTRv2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "chinese_cht_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ka_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "korean_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "latin_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "AutoEncoder_ad": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "DLinear_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "DLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "NLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "RLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-LCNet_x1_0_vehicle_attribute": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_16frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "DETR-R50": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "BlazeFace-FPN-SSH": ["paddle_mkldnn", "paddle"], "BlazeFace": ["paddle_mkldnn", "paddle"], "PP-YOLOE_plus-S_face": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-S": ["onnxruntime", "paddle"], "PP-YOLOE-L_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "STFPM": ["paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_small_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_tiny_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SOLOv2": ["onnxruntime", "paddle"], "PP-DocLayout-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-DocLayout-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L_layout_3cls": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-S_layout_17cls": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet_layout_1x": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle_mkldnn", "paddle"], "CenterNet-ResNet50": ["paddle_mkldnn", "paddle"], "FCOS-ResNet50": ["paddle_mkldnn", "paddle"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-XS": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-N": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-T": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOv3-DarkNet53": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-MobileNetV3": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_mkldnn", "paddle"], "PP-YOLOE-R-L": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-B": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-T": ["paddle_mkldnn", "paddle"], "SegFormer-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "SegFormer-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "SegFormer-B5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "PP-YOLOE_plus_SOD-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SLANeXt_wired": ["onnxruntime", "paddle"], "SLANeXt_wireless": ["onnxruntime", "paddle"], "SLANet_plus": ["onnxruntime", "paddle"], "SLANet": ["onnxruntime", "paddle"], "Nonstationary_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "PatchTST_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet_ad": ["onnxruntime", "paddle_mkldnn", "paddle"], "TimesNet_cls": ["onnxruntime", "paddle_mkldnn", "paddle"], "Nonstationary": ["onnxruntime", "paddle_mkldnn", "paddle"], "PatchTST": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-YOLOE-L_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["onnxruntime", "paddle"], "Mask-RT-DETR-H": ["paddle_mkldnn", "paddle"], "Mask-RT-DETR-L": ["paddle_mkldnn", "paddle"], "Mask-RT-DETR-M": ["paddle"], "Mask-RT-DETR-S": ["paddle"], "Mask-RT-DETR-X": ["paddle_mkldnn", "paddle"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle_mkldnn"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["onnxruntime", "paddle"], "MaskFormer_tiny": ["onnxruntime", "paddle"], "TiDE": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-DocBlockLayout": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-DocLayout_plus-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_rec": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "PP-OCRv5_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "paddle"], "PP-OCRv5_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-FormulaNet_plus-L": ["onnxruntime", "paddle"], "PP-FormulaNet_plus-M": ["onnxruntime", "paddle"], "PP-FormulaNet_plus-S": ["onnxruntime", "paddle"], "YOLO-Worldv2-L": ["paddle_mkldnn"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["paddle_mkldnn"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle31": {"PP-LCNet_x1_0_doc_ori": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "MobileFaceNet": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_face": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "CLIP_vit_large_patch14_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ConvNeXt_base_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_base_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-M": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_25": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_5": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x1_0": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x2_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_35": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_35": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_5": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_75": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_0": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_25": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_hybrid_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNet_base": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNet_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNet_tiny": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNetV2_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNetV2_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_25": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_35": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "PP-LCNet_x2_5": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "ResNet101": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet101_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet152": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet152_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet18": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet18_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet200_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ResNet34": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet34_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_448_ML": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B6_ML": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_0_ML": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "ResNet50_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-DocLayout-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-H_layout_17cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-H_layout_3cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "RT-DETR-H": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R18": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R50": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-X": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x1_0_pedestrian_attribute": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_server_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W18": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W48": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime"], "SeaFormer_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wired_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wireless_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x1_0_table_cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "arabic_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ch_SVTRv2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "chinese_cht_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "ka_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "korean_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "latin_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "AutoEncoder_ad": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "DLinear_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "DLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "NLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "RLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-LCNet_x1_0_vehicle_attribute": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_16frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "DETR-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "BlazeFace-FPN-SSH": ["paddle_mkldnn", "paddle"], "BlazeFace": ["paddle_mkldnn", "paddle"], "PP-YOLOE_plus-S_face": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-S": ["paddle"], "PP-YOLOE-L_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "STFPM": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_small_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_tiny_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SOLOv2": ["onnxruntime", "paddle"], "PP-DocLayout-M": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-DocLayout-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-L_layout_3cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S_layout_17cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_layout_1x": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle_mkldnn", "paddle"], "CenterNet-ResNet50": ["paddle_mkldnn", "paddle"], "FCOS-ResNet50": ["paddle_mkldnn", "onnxruntime", "paddle"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-M": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-XS": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-N": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-T": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOv3-DarkNet53": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-MobileNetV3": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_mkldnn", "paddle"], "PP-YOLOE-R-L": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-B": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-T": ["paddle_mkldnn", "paddle"], "SegFormer-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B2": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "SegFormer-B3": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime"], "SegFormer-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "SegFormer-B5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime"], "PP-YOLOE_plus_SOD-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SLANeXt_wired": ["onnxruntime", "paddle"], "SLANeXt_wireless": ["onnxruntime", "paddle"], "SLANet_plus": ["onnxruntime", "paddle"], "SLANet": ["onnxruntime", "paddle"], "Nonstationary_ad": ["onnxruntime", "paddle_mkldnn", "paddle"], "PatchTST_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet_ad": ["onnxruntime", "paddle_mkldnn", "paddle"], "TimesNet_cls": ["onnxruntime", "paddle_mkldnn", "paddle"], "Nonstationary": ["onnxruntime", "paddle_mkldnn", "paddle"], "PatchTST": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-YOLOE-L_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle_mkldnn", "onnxruntime", "paddle"], "Mask-RT-DETR-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "Mask-RT-DETR-M": ["onnxruntime", "paddle"], "Mask-RT-DETR-S": ["onnxruntime", "paddle"], "Mask-RT-DETR-X": ["onnxruntime", "paddle_mkldnn", "paddle"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle_mkldnn"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["onnxruntime", "paddle"], "MaskFormer_tiny": ["onnxruntime", "paddle"], "TiDE": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-DocBlockLayout": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-DocLayout_plus-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "paddle", "onnxruntime"], "PP-OCRv5_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle_mkldnn", "paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle311": {"PP-LCNet_x1_0_doc_ori": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "MobileFaceNet": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_face": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "CLIP_vit_large_patch14_224": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ConvNeXt_base_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_base_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_224": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_large_384": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ConvNeXt_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-M": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "FasterNet-T2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_5": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV1_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV1_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x0_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x1_5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV2_x2_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_35": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_5": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_large_x1_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_35": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_5": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x0_75": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV3_small_x1_25": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_conv_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_hybrid_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "MobileNetV4_hybrid_medium": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B5": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNet_base": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNet_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNet_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNetV2_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNetV2_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_25": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-LCNet_x0_35": ["onnxruntime", "paddle_mkldnn", "<PERSON>vin<PERSON>", "paddle"], "PP-LCNet_x0_5": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "PP-LCNet_x2_5": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "ResNet101": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet101_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet152": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet152_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle", "onnxruntime"], "ResNet18": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet18_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet200_vd": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ResNet34": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet34_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ResNet50_vd": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "StarNet-S4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "CLIP_vit_base_patch16_448_ML": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-HGNetV2-B6_ML": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x1_0_ML": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "ResNet50_ML": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-DocLayout-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-H_layout_17cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-H_layout_3cls": ["paddle_mkldnn", "onnxruntime", "paddle"], "PP-ShiTuV2_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "RT-DETR-H": ["paddle_mkldnn", "onnxruntime", "paddle"], "RT-DETR-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R18": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-R50": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-X": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x1_0_pedestrian_attribute": ["paddle_mkldnn", "onnxruntime", "<PERSON>vin<PERSON>", "paddle"], "PP-OCRv4_mobile_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_server_seal_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R101": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "Deeplabv3_Plus-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W18": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "OCRNet_HRNet-W48": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "SeaFormer_base": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_large": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_small": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SeaFormer_tiny": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wired_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "RT-DETR-L_wireless_table_cell_det": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-LCNet_x1_0_table_cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv4_server_det": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_mkldnn", "paddle", "<PERSON>vin<PERSON>", "onnxruntime"], "arabic_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "ch_SVTRv2_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "chinese_cht_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "korean_PP-OCRv3_mobile_rec": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "latin_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "AutoEncoder_ad": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "DLinear_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "DLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "NLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "RLinear": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-LCNet_x1_0_vehicle_attribute": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_16frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-TSMv2-LCNetV2_8frames_uniform": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "DETR-R50": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "BlazeFace-FPN-SSH": ["paddle_mkldnn", "paddle"], "BlazeFace": ["paddle_mkldnn", "paddle"], "PP-YOLOE_plus-S_face": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-S": ["paddle"], "PP-YOLOE-L_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_human": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "STFPM": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_base_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window12_384": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_large_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_small_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "SwinTransformer_tiny_patch4_window7_224": ["onnxruntime", "paddle_mkldnn", "paddle"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SOLOv2": ["onnxruntime", "paddle"], "PP-DocLayout-M": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-DocLayout-S": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-L_layout_3cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S_layout_17cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet_layout_1x": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle_mkldnn", "paddle"], "CenterNet-ResNet50": ["paddle_mkldnn", "paddle"], "FCOS-ResNet50": ["paddle_mkldnn", "onnxruntime", "paddle"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-L": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-M": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PicoDet-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PicoDet-XS": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-M": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-N": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-S": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOX-T": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "YOLOX-X": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "YOLOv3-DarkNet53": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-MobileNetV3": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_mkldnn", "paddle"], "PP-YOLOE-R-L": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-B": ["paddle_mkldnn", "paddle"], "PP-LiteSeg-T": ["paddle_mkldnn", "paddle"], "SegFormer-B0": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B1": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B2": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SegFormer-B3": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "SegFormer-B4": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "SegFormer-B5": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn"], "PP-YOLOE_plus_SOD-L": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "SLANeXt_wired": ["onnxruntime", "paddle"], "SLANeXt_wireless": ["onnxruntime", "paddle"], "SLANet_plus": ["onnxruntime", "paddle"], "SLANet": ["onnxruntime", "paddle"], "Nonstationary_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "PatchTST_ad": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet_ad": ["onnxruntime", "paddle_mkldnn", "paddle"], "TimesNet_cls": ["onnxruntime", "paddle_mkldnn", "paddle"], "Nonstationary": ["onnxruntime", "paddle_mkldnn", "paddle"], "PatchTST": ["onnxruntime", "paddle", "paddle_mkldnn"], "TimesNet": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-YOLOE-L_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-YOLOE-S_vehicle": ["<PERSON>vin<PERSON>", "paddle_mkldnn", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle_mkldnn", "onnxruntime", "paddle"], "Mask-RT-DETR-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "Mask-RT-DETR-M": ["onnxruntime", "paddle"], "Mask-RT-DETR-S": ["onnxruntime", "paddle"], "Mask-RT-DETR-X": ["onnxruntime", "paddle_mkldnn", "paddle"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle_mkldnn"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["onnxruntime", "paddle"], "MaskFormer_tiny": ["onnxruntime", "paddle"], "TiDE": ["onnxruntime", "<PERSON>vin<PERSON>", "paddle", "paddle_mkldnn"], "PP-DocBlockLayout": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-DocLayout_plus-L": ["onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_rec": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "paddle", "onnxruntime"], "PP-OCRv5_mobile_rec": ["<PERSON>vin<PERSON>", "onnxruntime", "paddle_mkldnn", "paddle"], "PP-OCRv5_server_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle_mkldnn", "paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["paddle_mkldnn", "<PERSON>vin<PERSON>", "onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}}, "gpu_cuda11": {"paddle30": {"PP-LCNet_x1_0_doc_ori": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle_tensorrt", "paddle", "onnxruntime"], "MobileFaceNet": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "ResNet50_face": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["tensorrt_fp16", "paddle_tensorrt_fp16", "paddle"], "CLIP_vit_large_patch14_224": ["tensorrt_fp16", "paddle_tensorrt_fp16", "paddle"], "ConvNeXt_base_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime", "tensorrt"], "ConvNeXt_base_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime", "tensorrt"], "ConvNeXt_large_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime", "tensorrt"], "ConvNeXt_large_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime", "tensorrt"], "ConvNeXt_small": ["paddle_tensorrt_fp16", "paddle", "onnxruntime", "tensorrt"], "ConvNeXt_tiny": ["paddle_tensorrt_fp16", "onnxruntime", "tensorrt", "paddle"], "FasterNet-L": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-M": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-S": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T0": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "FasterNet-T1": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "FasterNet-T2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x0_25": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV1_x0_5": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV1_x0_75": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x0_25": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "MobileNetV2_x0_5": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV2_x1_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x1_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x2_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV4_conv_large": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV4_conv_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_conv_small": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle"], "PP-HGNetV2-B0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B3": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-HGNetV2-B4": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-HGNet_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_tiny": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_large": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x0_25": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x0_35": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x0_5": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x2_5": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ResNet101": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet101_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet18": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ResNet18_vd": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ResNet200_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ResNet50_vd": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S1": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "StarNet-S2": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "StarNet-S3": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "StarNet-S4": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["tensorrt", "paddle_tensorrt", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["paddle", "paddle_tensorrt", "tensorrt"], "PP-ShiTuV2_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["tensorrt_fp16", "paddle_tensorrt_fp16", "paddle"], "PP-HGNetV2-B0_ML": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ResNet50_ML": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["tensorrt", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["tensorrt", "onnxruntime", "paddle"], "PP-DocLayout-L": ["paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "RT-DETR-H": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R50": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-X": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "PP-OCRv4_server_seal_det": ["tensorrt", "onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["tensorrt_fp16", "paddle_tensorrt", "paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle_tensorrt", "paddle", "tensorrt", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_large": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "SeaFormer_small": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_tiny": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "RT-DETR-L_wired_table_cell_det": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv3_server_det": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv4_server_det": ["tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "arabic_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ch_SVTRv2_rec": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "chinese_cht_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "AutoEncoder_ad": ["tensorrt", "onnxruntime", "paddle_tensorrt", "paddle"], "DLinear_ad": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "DLinear": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "NLinear": ["tensorrt", "onnxruntime", "paddle_tensorrt", "paddle"], "RLinear": ["tensorrt_fp16", "onnxruntime", "paddle_tensorrt_fp16", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["tensorrt", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["tensorrt", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "DETR-R50": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle_tensorrt_fp16", "paddle"], "BlazeFace": ["paddle_tensorrt", "paddle"], "PP-YOLOE_plus-S_face": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle", "paddle_tensorrt_fp16"], "PP-YOLOE-L_human": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE-S_human": ["paddle_tensorrt", "onnxruntime", "paddle"], "STFPM": ["paddle_tensorrt_fp16", "paddle"], "SwinTransformer_base_patch4_window12_384": ["paddle_tensorrt", "paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "PP-DocLayout-S": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle_tensorrt", "paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle_tensorrt_fp16", "paddle"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-XS": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-M": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "YOLOX-N": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-S": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-T": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle_tensorrt", "paddle"], "YOLOv3-MobileNetV3": ["paddle_tensorrt_fp16", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_tensorrt", "paddle"], "PP-YOLOE-R-L": ["paddle_tensorrt", "paddle"], "PP-LiteSeg-B": ["paddle", "paddle_tensorrt"], "PP-LiteSeg-T": ["paddle_tensorrt", "paddle"], "SegFormer-B0": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B1": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B2": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B3": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B4": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B5": ["paddle", "onnxruntime", "paddle_tensorrt"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle_tensorrt", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle_tensorrt_fp16", "paddle"], "SLANeXt_wired": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANeXt_wireless": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANet_plus": ["paddle_tensorrt", "paddle", "onnxruntime"], "SLANet": ["paddle_tensorrt", "paddle", "onnxruntime"], "Nonstationary_ad": ["onnxruntime", "paddle"], "PatchTST_ad": ["paddle_tensorrt", "onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["onnxruntime", "paddle_tensorrt", "paddle"], "PatchTST": ["paddle_tensorrt", "onnxruntime", "paddle"], "TimesNet": ["onnxruntime", "paddle"], "PP-YOLOE-L_vehicle": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["paddle_tensorrt", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle", "paddle_tensorrt_fp16"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle"], "Mask-RT-DETR-L": ["paddle"], "Mask-RT-DETR-M": ["paddle"], "Mask-RT-DETR-S": ["paddle"], "Mask-RT-DETR-X": ["paddle"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["tensorrt", "paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv5_server_det": ["tensorrt", "paddle"], "PP-OCRv5_mobile_det": ["paddle_tensorrt", "paddle"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle31": {"PP-LCNet_x1_0_doc_ori": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle_tensorrt", "paddle", "onnxruntime"], "MobileFaceNet": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50_face": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["tensorrt_fp16", "paddle_tensorrt_fp16", "paddle", "onnxruntime"], "CLIP_vit_large_patch14_224": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ConvNeXt_base_224": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_base_384": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_large_224": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_large_384": ["paddle", "onnxruntime", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_small": ["paddle", "onnxruntime", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_tiny": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "FasterNet-L": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "FasterNet-M": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-S": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x0_25": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x0_5": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "MobileNetV1_x0_75": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x0_25": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "MobileNetV2_x0_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x1_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x1_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x2_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["paddle_tensorrt", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_conv_large": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_conv_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_conv_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle"], "PP-HGNetV2-B0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B3": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-HGNetV2-B4": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-HGNet_tiny": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_large": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_25": ["tensorrt", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_35": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x2_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet101": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet101_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet18": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet18_vd": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet200_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S3": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S4": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["tensorrt", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["tensorrt", "onnxruntime", "paddle"], "PP-DocLayout-L": ["tensorrt", "paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "RT-DETR-H": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R50": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-X": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle_tensorrt", "paddle", "tensorrt", "onnxruntime"], "PP-OCRv4_server_seal_det": ["tensorrt", "onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["tensorrt_fp16", "paddle_tensorrt", "paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_large": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_small": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_tiny": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "RT-DETR-L_wired_table_cell_det": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv3_server_det": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "PP-OCRv4_server_det": ["tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "arabic_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ch_SVTRv2_rec": ["paddle", "onnxruntime"], "chinese_cht_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["tensorrt", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "AutoEncoder_ad": ["tensorrt", "onnxruntime", "paddle", "paddle_tensorrt_fp16"], "DLinear_ad": ["tensorrt", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "DLinear": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "NLinear": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "RLinear": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "DETR-R50": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle_tensorrt", "paddle"], "BlazeFace": ["paddle", "paddle_tensorrt"], "PP-YOLOE_plus-S_face": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle", "paddle_tensorrt_fp16"], "PP-YOLOE-L_human": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE-S_human": ["paddle_tensorrt", "onnxruntime", "paddle"], "STFPM": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "SwinTransformer_base_patch4_window12_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle", "paddle_tensorrt", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-DocLayout-S": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S_layout_3cls": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle_tensorrt", "paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-XS": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-M": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "YOLOX-N": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-S": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-T": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle_tensorrt", "paddle"], "YOLOv3-MobileNetV3": ["paddle_tensorrt_fp16", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_tensorrt", "paddle"], "PP-YOLOE-R-L": ["paddle_tensorrt", "paddle"], "PP-LiteSeg-B": ["paddle_tensorrt", "paddle"], "PP-LiteSeg-T": ["paddle_tensorrt", "paddle"], "SegFormer-B0": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B1": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SegFormer-B2": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B3": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B4": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B5": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle_tensorrt", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle_tensorrt", "paddle"], "SLANeXt_wired": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANeXt_wireless": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANet_plus": ["paddle_tensorrt", "paddle", "onnxruntime"], "SLANet": ["paddle_tensorrt", "paddle", "onnxruntime"], "Nonstationary_ad": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PatchTST_ad": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PatchTST": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "TimesNet": ["onnxruntime", "paddle"], "PP-YOLOE-L_vehicle": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["paddle_tensorrt", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle_tensorrt_fp16", "paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle_tensorrt_fp16", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle", "onnxruntime"], "Mask-RT-DETR-L": ["paddle", "onnxruntime"], "Mask-RT-DETR-M": ["paddle", "onnxruntime"], "Mask-RT-DETR-S": ["paddle", "onnxruntime"], "Mask-RT-DETR-X": ["paddle", "onnxruntime"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["tensorrt", "paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["tensorrt", "paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv5_server_det": ["tensorrt", "onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle311": {"PP-LCNet_x1_0_doc_ori": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle_tensorrt", "paddle", "onnxruntime"], "MobileFaceNet": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "ResNet50_face": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "CLIP_vit_large_patch14_224": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ConvNeXt_base_224": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_base_384": ["paddle", "onnxruntime", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_large_224": ["paddle", "onnxruntime", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_large_384": ["paddle", "onnxruntime", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_small": ["onnxruntime", "paddle", "paddle_tensorrt_fp16", "tensorrt"], "ConvNeXt_tiny": ["onnxruntime", "tensorrt", "paddle_tensorrt", "paddle"], "FasterNet-L": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-M": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-S": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "FasterNet-T2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x0_25": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x0_5": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "MobileNetV1_x0_75": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV1_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x0_25": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "MobileNetV2_x0_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x1_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x1_5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV2_x2_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_conv_large": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "MobileNetV4_conv_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "MobileNetV4_conv_small": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["paddle_tensorrt", "tensorrt_fp16", "onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle"], "PP-HGNetV2-B0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B3": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-HGNetV2-B4": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B5": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B6": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNet_tiny": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_base": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_large": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNetV2_small": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_25": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_35": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_75": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x1_0": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x1_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x2_0": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x2_5": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet101": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet101_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet152_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet18": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet18_vd": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet200_vd": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet34_vd": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ResNet50_vd": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S1": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S2": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S3": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "StarNet-S4": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "ResNet50_ML": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["tensorrt", "onnxruntime", "paddle"], "PP-TinyPose_256x192": ["tensorrt", "onnxruntime", "paddle"], "PP-DocLayout-L": ["tensorrt", "paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "RT-DETR-H": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-R50": ["paddle_tensorrt_fp16", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-X": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle_tensorrt_fp16", "paddle", "tensorrt", "onnxruntime"], "PP-OCRv4_server_seal_det": ["tensorrt", "onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_large": ["onnxruntime", "paddle_tensorrt_fp16", "paddle", "tensorrt"], "SeaFormer_small": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "SeaFormer_tiny": ["onnxruntime", "paddle", "paddle_tensorrt", "tensorrt"], "RT-DETR-L_wired_table_cell_det": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle_tensorrt", "tensorrt", "paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv3_server_det": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv4_server_det": ["tensorrt", "onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "arabic_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ch_RepSVTR_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ch_SVTRv2_rec": ["onnxruntime", "paddle"], "chinese_cht_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["paddle_tensorrt", "tensorrt", "onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["tensorrt", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "AutoEncoder_ad": ["tensorrt", "onnxruntime", "paddle", "paddle_tensorrt"], "DLinear_ad": ["tensorrt", "paddle_tensorrt", "onnxruntime", "paddle"], "DLinear": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "NLinear": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "RLinear": ["tensorrt", "onnxruntime", "paddle_tensorrt", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["tensorrt_fp16", "paddle_tensorrt", "onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["tensorrt_fp16", "paddle", "onnxruntime"], "DETR-R50": ["paddle_tensorrt", "paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle_tensorrt", "paddle"], "BlazeFace": ["paddle_tensorrt_fp16", "paddle"], "PP-YOLOE_plus-S_face": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle", "paddle_tensorrt_fp16"], "PP-YOLOE-L_human": ["paddle_tensorrt", "onnxruntime", "paddle"], "PP-YOLOE-S_human": ["paddle_tensorrt", "onnxruntime", "paddle"], "STFPM": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_base_patch4_window12_384": ["paddle_tensorrt", "paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-DocLayout-S": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["paddle_tensorrt", "onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle_tensorrt", "paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle_tensorrt_fp16", "paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-M": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-S": ["paddle_tensorrt", "paddle", "onnxruntime"], "PicoDet-XS": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-L": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOX-M": ["onnxruntime", "paddle", "paddle_tensorrt_fp16"], "YOLOX-N": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-S": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-T": ["onnxruntime", "paddle_tensorrt", "paddle"], "YOLOX-X": ["paddle_tensorrt", "paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle_tensorrt", "paddle"], "YOLOv3-MobileNetV3": ["paddle_tensorrt", "paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle_tensorrt", "paddle"], "PP-YOLOE-R-L": ["paddle_tensorrt", "paddle"], "PP-LiteSeg-B": ["paddle_tensorrt", "paddle"], "PP-LiteSeg-T": ["paddle_tensorrt", "paddle"], "SegFormer-B0": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B1": ["paddle", "onnxruntime", "paddle_tensorrt"], "SegFormer-B2": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B3": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B4": ["onnxruntime", "paddle", "paddle_tensorrt"], "SegFormer-B5": ["onnxruntime", "paddle", "paddle_tensorrt"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle_tensorrt", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle_tensorrt", "paddle"], "SLANeXt_wired": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANeXt_wireless": ["paddle", "paddle_tensorrt", "onnxruntime"], "SLANet_plus": ["paddle_tensorrt", "paddle", "onnxruntime"], "SLANet": ["paddle_tensorrt", "paddle", "onnxruntime"], "Nonstationary_ad": ["onnxruntime", "paddle"], "PatchTST_ad": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "PatchTST": ["paddle_tensorrt_fp16", "onnxruntime", "paddle"], "TimesNet": ["paddle", "onnxruntime"], "PP-YOLOE-L_vehicle": ["paddle_tensorrt", "paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["paddle_tensorrt", "onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle_tensorrt_fp16", "paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle_tensorrt_fp16", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle", "onnxruntime"], "Mask-RT-DETR-L": ["paddle", "onnxruntime"], "Mask-RT-DETR-M": ["paddle", "onnxruntime"], "Mask-RT-DETR-S": ["paddle", "onnxruntime"], "Mask-RT-DETR-X": ["paddle", "onnxruntime"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["tensorrt", "paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["tensorrt", "paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["paddle_tensorrt_fp16", "tensorrt_fp16", "onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["paddle_tensorrt_fp16", "tensorrt", "onnxruntime", "paddle"], "PP-OCRv5_server_det": ["tensorrt", "onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle_tensorrt_fp16", "tensorrt_fp16", "paddle", "onnxruntime"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["tensorrt_fp16", "paddle_tensorrt_fp16", "onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}}, "gpu_cuda12": {"paddle30": {"PP-LCNet_x1_0_doc_ori": ["onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle", "onnxruntime"], "MobileFaceNet": ["onnxruntime", "paddle"], "ResNet50_face": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["paddle"], "CLIP_vit_large_patch14_224": ["paddle"], "ConvNeXt_base_224": ["paddle", "onnxruntime"], "ConvNeXt_base_384": ["paddle", "onnxruntime"], "ConvNeXt_large_224": ["paddle", "onnxruntime"], "ConvNeXt_large_384": ["paddle", "onnxruntime"], "ConvNeXt_small": ["paddle", "onnxruntime"], "ConvNeXt_tiny": ["onnxruntime", "paddle"], "FasterNet-L": ["onnxruntime", "paddle"], "FasterNet-M": ["onnxruntime", "paddle"], "FasterNet-S": ["onnxruntime", "paddle"], "FasterNet-T0": ["onnxruntime", "paddle"], "FasterNet-T1": ["onnxruntime", "paddle"], "FasterNet-T2": ["onnxruntime", "paddle"], "MobileNetV1_x0_25": ["onnxruntime", "paddle"], "MobileNetV1_x0_5": ["onnxruntime", "paddle"], "MobileNetV1_x0_75": ["onnxruntime", "paddle"], "MobileNetV1_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x0_25": ["onnxruntime", "paddle"], "MobileNetV2_x0_5": ["onnxruntime", "paddle"], "MobileNetV2_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x1_5": ["onnxruntime", "paddle"], "MobileNetV2_x2_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["onnxruntime", "paddle"], "MobileNetV4_conv_large": ["onnxruntime", "paddle"], "MobileNetV4_conv_medium": ["onnxruntime", "paddle"], "MobileNetV4_conv_small": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle"], "PP-HGNetV2-B0": ["onnxruntime", "paddle"], "PP-HGNetV2-B1": ["onnxruntime", "paddle"], "PP-HGNetV2-B2": ["onnxruntime", "paddle"], "PP-HGNetV2-B3": ["onnxruntime", "paddle"], "PP-HGNetV2-B4": ["onnxruntime", "paddle"], "PP-HGNetV2-B5": ["onnxruntime", "paddle"], "PP-HGNetV2-B6": ["onnxruntime", "paddle"], "PP-HGNet_base": ["paddle", "onnxruntime"], "PP-HGNet_small": ["onnxruntime", "paddle"], "PP-HGNet_tiny": ["onnxruntime", "paddle"], "PP-LCNetV2_base": ["onnxruntime", "paddle"], "PP-LCNetV2_large": ["onnxruntime", "paddle"], "PP-LCNetV2_small": ["onnxruntime", "paddle"], "PP-LCNet_x0_25": ["onnxruntime", "paddle"], "PP-LCNet_x0_35": ["onnxruntime", "paddle"], "PP-LCNet_x0_5": ["onnxruntime", "paddle"], "PP-LCNet_x0_75": ["onnxruntime", "paddle"], "PP-LCNet_x1_0": ["onnxruntime", "paddle"], "PP-LCNet_x1_5": ["onnxruntime", "paddle"], "PP-LCNet_x2_0": ["onnxruntime", "paddle"], "PP-LCNet_x2_5": ["onnxruntime", "paddle"], "ResNet101": ["onnxruntime", "paddle"], "ResNet101_vd": ["onnxruntime", "paddle"], "ResNet152": ["onnxruntime", "paddle"], "ResNet152_vd": ["onnxruntime", "paddle"], "ResNet18": ["onnxruntime", "paddle"], "ResNet18_vd": ["onnxruntime", "paddle"], "ResNet200_vd": ["onnxruntime", "paddle"], "ResNet34": ["onnxruntime", "paddle"], "ResNet34_vd": ["onnxruntime", "paddle"], "ResNet50": ["onnxruntime", "paddle"], "ResNet50_vd": ["onnxruntime", "paddle"], "StarNet-S1": ["onnxruntime", "paddle"], "StarNet-S2": ["onnxruntime", "paddle"], "StarNet-S3": ["onnxruntime", "paddle"], "StarNet-S4": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["paddle"], "PP-ShiTuV2_rec": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["paddle"], "PP-HGNetV2-B0_ML": ["onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["onnxruntime", "paddle"], "ResNet50_ML": ["paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["onnxruntime", "paddle"], "PP-TinyPose_256x192": ["onnxruntime", "paddle"], "PP-DocLayout-L": ["paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle", "onnxruntime"], "RT-DETR-H": ["paddle", "onnxruntime"], "RT-DETR-L": ["paddle", "onnxruntime"], "RT-DETR-R18": ["paddle", "onnxruntime"], "RT-DETR-R50": ["paddle", "onnxruntime"], "RT-DETR-X": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_seal_det": ["onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle"], "SeaFormer_large": ["onnxruntime", "paddle"], "SeaFormer_small": ["onnxruntime", "paddle"], "SeaFormer_tiny": ["onnxruntime", "paddle"], "RT-DETR-L_wired_table_cell_det": ["paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv3_server_det": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_det": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["paddle", "onnxruntime"], "arabic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ch_RepSVTR_rec": ["onnxruntime", "paddle"], "ch_SVTRv2_rec": ["paddle", "onnxruntime"], "chinese_cht_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["onnxruntime", "paddle"], "AutoEncoder_ad": ["onnxruntime", "paddle"], "DLinear_ad": ["onnxruntime", "paddle"], "DLinear": ["onnxruntime", "paddle"], "NLinear": ["onnxruntime", "paddle"], "RLinear": ["onnxruntime", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["paddle", "onnxruntime"], "DETR-R50": ["paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle"], "BlazeFace": ["paddle"], "PP-YOLOE_plus-S_face": ["paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle"], "PP-YOLOE-L_human": ["paddle", "onnxruntime"], "PP-YOLOE-S_human": ["onnxruntime", "paddle"], "STFPM": ["paddle"], "SwinTransformer_base_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle", "onnxruntime"], "PP-DocLayout-S": ["onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle", "onnxruntime"], "PicoDet-L": ["paddle", "onnxruntime"], "PicoDet-M": ["paddle", "onnxruntime"], "PicoDet-S": ["paddle", "onnxruntime"], "PicoDet-XS": ["paddle", "onnxruntime"], "YOLOX-L": ["paddle", "onnxruntime"], "YOLOX-M": ["onnxruntime", "paddle"], "YOLOX-N": ["onnxruntime", "paddle"], "YOLOX-S": ["onnxruntime", "paddle"], "YOLOX-T": ["onnxruntime", "paddle"], "YOLOX-X": ["paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle"], "YOLOv3-MobileNetV3": ["paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle"], "PP-YOLOE-R-L": ["paddle"], "PP-LiteSeg-B": ["paddle"], "PP-LiteSeg-T": ["paddle"], "SegFormer-B0": ["paddle", "onnxruntime"], "SegFormer-B1": ["paddle", "onnxruntime"], "SegFormer-B2": ["paddle", "onnxruntime"], "SegFormer-B3": ["paddle", "onnxruntime"], "SegFormer-B4": ["paddle", "onnxruntime"], "SegFormer-B5": ["paddle", "onnxruntime"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle"], "SLANeXt_wired": ["paddle", "onnxruntime"], "SLANeXt_wireless": ["paddle", "onnxruntime"], "SLANet_plus": ["paddle", "onnxruntime"], "SLANet": ["paddle", "onnxruntime"], "Nonstationary_ad": ["onnxruntime", "paddle"], "PatchTST_ad": ["onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["onnxruntime", "paddle"], "PatchTST": ["onnxruntime", "paddle"], "TimesNet": ["onnxruntime", "paddle"], "PP-YOLOE-L_vehicle": ["paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle"], "Mask-RT-DETR-L": ["paddle"], "Mask-RT-DETR-M": ["paddle"], "Mask-RT-DETR-S": ["paddle"], "Mask-RT-DETR-X": ["paddle"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv5_server_det": ["paddle"], "PP-OCRv5_mobile_det": ["paddle"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle31": {"PP-LCNet_x1_0_doc_ori": ["onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle", "onnxruntime"], "MobileFaceNet": ["onnxruntime", "paddle"], "ResNet50_face": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["paddle", "onnxruntime"], "CLIP_vit_large_patch14_224": ["onnxruntime", "paddle"], "ConvNeXt_base_224": ["onnxruntime", "paddle"], "ConvNeXt_base_384": ["onnxruntime", "paddle"], "ConvNeXt_large_224": ["onnxruntime", "paddle"], "ConvNeXt_large_384": ["paddle", "onnxruntime"], "ConvNeXt_small": ["paddle", "onnxruntime"], "ConvNeXt_tiny": ["onnxruntime", "paddle"], "FasterNet-L": ["paddle", "onnxruntime"], "FasterNet-M": ["onnxruntime", "paddle"], "FasterNet-S": ["onnxruntime", "paddle"], "FasterNet-T0": ["onnxruntime", "paddle"], "FasterNet-T1": ["onnxruntime", "paddle"], "FasterNet-T2": ["onnxruntime", "paddle"], "MobileNetV1_x0_25": ["onnxruntime", "paddle"], "MobileNetV1_x0_5": ["onnxruntime", "paddle"], "MobileNetV1_x0_75": ["onnxruntime", "paddle"], "MobileNetV1_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x0_25": ["onnxruntime", "paddle"], "MobileNetV2_x0_5": ["onnxruntime", "paddle"], "MobileNetV2_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x1_5": ["onnxruntime", "paddle"], "MobileNetV2_x2_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["onnxruntime", "paddle"], "MobileNetV4_conv_large": ["onnxruntime", "paddle"], "MobileNetV4_conv_medium": ["onnxruntime", "paddle"], "MobileNetV4_conv_small": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle"], "PP-HGNetV2-B0": ["onnxruntime", "paddle"], "PP-HGNetV2-B1": ["onnxruntime", "paddle"], "PP-HGNetV2-B2": ["onnxruntime", "paddle"], "PP-HGNetV2-B3": ["onnxruntime", "paddle"], "PP-HGNetV2-B4": ["onnxruntime", "paddle"], "PP-HGNetV2-B5": ["onnxruntime", "paddle"], "PP-HGNetV2-B6": ["onnxruntime", "paddle"], "PP-HGNet_base": ["onnxruntime", "paddle"], "PP-HGNet_small": ["paddle", "onnxruntime"], "PP-HGNet_tiny": ["onnxruntime", "paddle"], "PP-LCNetV2_base": ["onnxruntime", "paddle"], "PP-LCNetV2_large": ["onnxruntime", "paddle"], "PP-LCNetV2_small": ["onnxruntime", "paddle"], "PP-LCNet_x0_25": ["onnxruntime", "paddle"], "PP-LCNet_x0_35": ["onnxruntime", "paddle"], "PP-LCNet_x0_5": ["onnxruntime", "paddle"], "PP-LCNet_x0_75": ["onnxruntime", "paddle"], "PP-LCNet_x1_0": ["onnxruntime", "paddle"], "PP-LCNet_x1_5": ["onnxruntime", "paddle"], "PP-LCNet_x2_0": ["onnxruntime", "paddle"], "PP-LCNet_x2_5": ["onnxruntime", "paddle"], "ResNet101": ["onnxruntime", "paddle"], "ResNet101_vd": ["onnxruntime", "paddle"], "ResNet152": ["onnxruntime", "paddle"], "ResNet152_vd": ["onnxruntime", "paddle"], "ResNet18": ["onnxruntime", "paddle"], "ResNet18_vd": ["onnxruntime", "paddle"], "ResNet200_vd": ["onnxruntime", "paddle"], "ResNet34": ["onnxruntime", "paddle"], "ResNet34_vd": ["onnxruntime", "paddle"], "ResNet50": ["onnxruntime", "paddle"], "ResNet50_vd": ["onnxruntime", "paddle"], "StarNet-S1": ["onnxruntime", "paddle"], "StarNet-S2": ["onnxruntime", "paddle"], "StarNet-S3": ["onnxruntime", "paddle"], "StarNet-S4": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["onnxruntime", "paddle"], "ResNet50_ML": ["paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["onnxruntime", "paddle"], "PP-TinyPose_256x192": ["onnxruntime", "paddle"], "PP-DocLayout-L": ["paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle", "onnxruntime"], "RT-DETR-H": ["paddle", "onnxruntime"], "RT-DETR-L": ["paddle", "onnxruntime"], "RT-DETR-R18": ["paddle", "onnxruntime"], "RT-DETR-R50": ["paddle", "onnxruntime"], "RT-DETR-X": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_seal_det": ["onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle"], "SeaFormer_large": ["onnxruntime", "paddle"], "SeaFormer_small": ["onnxruntime", "paddle"], "SeaFormer_tiny": ["onnxruntime", "paddle"], "RT-DETR-L_wired_table_cell_det": ["paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv3_server_det": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_det": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["onnxruntime", "paddle"], "arabic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ch_RepSVTR_rec": ["onnxruntime", "paddle"], "ch_SVTRv2_rec": ["paddle", "onnxruntime"], "chinese_cht_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["onnxruntime", "paddle"], "AutoEncoder_ad": ["onnxruntime", "paddle"], "DLinear_ad": ["onnxruntime", "paddle"], "DLinear": ["onnxruntime", "paddle"], "NLinear": ["onnxruntime", "paddle"], "RLinear": ["onnxruntime", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["paddle", "onnxruntime"], "DETR-R50": ["paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle"], "BlazeFace": ["paddle"], "PP-YOLOE_plus-S_face": ["paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle"], "PP-YOLOE-L_human": ["paddle", "onnxruntime"], "PP-YOLOE-S_human": ["onnxruntime", "paddle"], "STFPM": ["onnxruntime", "paddle"], "SwinTransformer_base_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle", "onnxruntime"], "PP-DocLayout-S": ["onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["paddle", "onnxruntime"], "PicoDet-S_layout_3cls": ["onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle", "onnxruntime"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle", "onnxruntime"], "PicoDet-L": ["paddle", "onnxruntime"], "PicoDet-M": ["paddle", "onnxruntime"], "PicoDet-S": ["paddle", "onnxruntime"], "PicoDet-XS": ["paddle", "onnxruntime"], "YOLOX-L": ["paddle", "onnxruntime"], "YOLOX-M": ["onnxruntime", "paddle"], "YOLOX-N": ["onnxruntime", "paddle"], "YOLOX-S": ["onnxruntime", "paddle"], "YOLOX-T": ["onnxruntime", "paddle"], "YOLOX-X": ["paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle"], "YOLOv3-MobileNetV3": ["paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle"], "PP-YOLOE-R-L": ["paddle"], "PP-LiteSeg-B": ["paddle"], "PP-LiteSeg-T": ["paddle"], "SegFormer-B0": ["paddle", "onnxruntime"], "SegFormer-B1": ["paddle", "onnxruntime"], "SegFormer-B2": ["onnxruntime", "paddle"], "SegFormer-B3": ["onnxruntime", "paddle"], "SegFormer-B4": ["onnxruntime", "paddle"], "SegFormer-B5": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle"], "SLANeXt_wired": ["paddle", "onnxruntime"], "SLANeXt_wireless": ["paddle", "onnxruntime"], "SLANet_plus": ["paddle", "onnxruntime"], "SLANet": ["paddle", "onnxruntime"], "Nonstationary_ad": ["onnxruntime", "paddle"], "PatchTST_ad": ["onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["onnxruntime", "paddle"], "PatchTST": ["onnxruntime", "paddle"], "TimesNet": ["onnxruntime", "paddle"], "PP-YOLOE-L_vehicle": ["paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle", "onnxruntime"], "Mask-RT-DETR-L": ["paddle", "onnxruntime"], "Mask-RT-DETR-M": ["paddle", "onnxruntime"], "Mask-RT-DETR-S": ["paddle", "onnxruntime"], "Mask-RT-DETR-X": ["paddle", "onnxruntime"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv5_server_det": ["onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle", "onnxruntime"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}, "paddle311": {"PP-LCNet_x1_0_doc_ori": ["onnxruntime", "paddle"], "PicoDet_LCNet_x2_5_face": ["paddle", "onnxruntime"], "MobileFaceNet": ["onnxruntime", "paddle"], "ResNet50_face": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_224": ["paddle", "onnxruntime"], "CLIP_vit_large_patch14_224": ["onnxruntime", "paddle"], "ConvNeXt_base_224": ["onnxruntime", "paddle"], "ConvNeXt_base_384": ["paddle", "onnxruntime"], "ConvNeXt_large_224": ["paddle", "onnxruntime"], "ConvNeXt_large_384": ["paddle", "onnxruntime"], "ConvNeXt_small": ["onnxruntime", "paddle"], "ConvNeXt_tiny": ["onnxruntime", "paddle"], "FasterNet-L": ["onnxruntime", "paddle"], "FasterNet-M": ["onnxruntime", "paddle"], "FasterNet-S": ["onnxruntime", "paddle"], "FasterNet-T0": ["onnxruntime", "paddle"], "FasterNet-T1": ["onnxruntime", "paddle"], "FasterNet-T2": ["onnxruntime", "paddle"], "MobileNetV1_x0_25": ["onnxruntime", "paddle"], "MobileNetV1_x0_5": ["onnxruntime", "paddle"], "MobileNetV1_x0_75": ["onnxruntime", "paddle"], "MobileNetV1_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x0_25": ["onnxruntime", "paddle"], "MobileNetV2_x0_5": ["onnxruntime", "paddle"], "MobileNetV2_x1_0": ["onnxruntime", "paddle"], "MobileNetV2_x1_5": ["onnxruntime", "paddle"], "MobileNetV2_x2_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_large_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_large_x1_25": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_35": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_5": ["onnxruntime", "paddle"], "MobileNetV3_small_x0_75": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_0": ["onnxruntime", "paddle"], "MobileNetV3_small_x1_25": ["onnxruntime", "paddle"], "MobileNetV4_conv_large": ["paddle", "onnxruntime"], "MobileNetV4_conv_medium": ["paddle", "onnxruntime"], "MobileNetV4_conv_small": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_large": ["onnxruntime", "paddle"], "MobileNetV4_hybrid_medium": ["paddle"], "PP-HGNetV2-B0": ["onnxruntime", "paddle"], "PP-HGNetV2-B1": ["onnxruntime", "paddle"], "PP-HGNetV2-B2": ["onnxruntime", "paddle"], "PP-HGNetV2-B3": ["onnxruntime", "paddle"], "PP-HGNetV2-B4": ["onnxruntime", "paddle"], "PP-HGNetV2-B5": ["onnxruntime", "paddle"], "PP-HGNetV2-B6": ["onnxruntime", "paddle"], "PP-HGNet_base": ["onnxruntime", "paddle"], "PP-HGNet_small": ["onnxruntime", "paddle"], "PP-HGNet_tiny": ["onnxruntime", "paddle"], "PP-LCNetV2_base": ["onnxruntime", "paddle"], "PP-LCNetV2_large": ["onnxruntime", "paddle"], "PP-LCNetV2_small": ["onnxruntime", "paddle"], "PP-LCNet_x0_25": ["onnxruntime", "paddle"], "PP-LCNet_x0_35": ["onnxruntime", "paddle"], "PP-LCNet_x0_5": ["onnxruntime", "paddle"], "PP-LCNet_x0_75": ["onnxruntime", "paddle"], "PP-LCNet_x1_0": ["onnxruntime", "paddle"], "PP-LCNet_x1_5": ["onnxruntime", "paddle"], "PP-LCNet_x2_0": ["onnxruntime", "paddle"], "PP-LCNet_x2_5": ["onnxruntime", "paddle"], "ResNet101": ["onnxruntime", "paddle"], "ResNet101_vd": ["onnxruntime", "paddle"], "ResNet152": ["onnxruntime", "paddle"], "ResNet152_vd": ["onnxruntime", "paddle"], "ResNet18": ["onnxruntime", "paddle"], "ResNet18_vd": ["onnxruntime", "paddle"], "ResNet200_vd": ["onnxruntime", "paddle"], "ResNet34": ["onnxruntime", "paddle"], "ResNet34_vd": ["onnxruntime", "paddle"], "ResNet50": ["onnxruntime", "paddle"], "ResNet50_vd": ["onnxruntime", "paddle"], "StarNet-S1": ["onnxruntime", "paddle"], "StarNet-S2": ["onnxruntime", "paddle"], "StarNet-S3": ["onnxruntime", "paddle"], "StarNet-S4": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_base": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec_CLIP_vit_large": ["onnxruntime", "paddle"], "PP-ShiTuV2_rec": ["onnxruntime", "paddle"], "CLIP_vit_base_patch16_448_ML": ["onnxruntime", "paddle"], "PP-HGNetV2-B0_ML": ["onnxruntime", "paddle"], "PP-HGNetV2-B4_ML": ["paddle", "onnxruntime"], "PP-HGNetV2-B6_ML": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_ML": ["paddle", "onnxruntime"], "ResNet50_ML": ["paddle", "onnxruntime"], "UVDoc": ["paddle"], "PP-TinyPose_128x96": ["onnxruntime", "paddle"], "PP-TinyPose_256x192": ["onnxruntime", "paddle"], "PP-DocLayout-L": ["paddle", "onnxruntime"], "RT-DETR-H_layout_17cls": ["paddle", "onnxruntime"], "RT-DETR-H_layout_3cls": ["paddle", "onnxruntime"], "PP-ShiTuV2_det": ["paddle", "onnxruntime"], "RT-DETR-H": ["paddle", "onnxruntime"], "RT-DETR-L": ["paddle", "onnxruntime"], "RT-DETR-R18": ["paddle", "onnxruntime"], "RT-DETR-R50": ["paddle", "onnxruntime"], "RT-DETR-X": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_pedestrian_attribute": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_seal_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_seal_det": ["onnxruntime", "paddle"], "Deeplabv3-R101": ["paddle", "onnxruntime"], "Deeplabv3-R50": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R101": ["paddle", "onnxruntime"], "Deeplabv3_Plus-R50": ["paddle", "onnxruntime"], "OCRNet_HRNet-W18": ["paddle", "onnxruntime"], "OCRNet_HRNet-W48": ["paddle", "onnxruntime"], "SeaFormer_base": ["onnxruntime", "paddle"], "SeaFormer_large": ["onnxruntime", "paddle"], "SeaFormer_small": ["onnxruntime", "paddle"], "SeaFormer_tiny": ["onnxruntime", "paddle"], "RT-DETR-L_wired_table_cell_det": ["paddle", "onnxruntime"], "RT-DETR-L_wireless_table_cell_det": ["paddle", "onnxruntime"], "PP-LCNet_x1_0_table_cls": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv3_server_det": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_det": ["paddle", "onnxruntime"], "PP-OCRv4_server_det": ["onnxruntime", "paddle"], "PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv4_server_rec_doc": ["paddle", "onnxruntime"], "PP-OCRv4_server_rec": ["onnxruntime", "paddle"], "arabic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ch_RepSVTR_rec": ["onnxruntime", "paddle"], "ch_SVTRv2_rec": ["onnxruntime", "paddle"], "chinese_cht_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "cyrillic_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "devanagari_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "en_PP-OCRv4_mobile_rec": ["onnxruntime", "paddle"], "japan_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ka_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "korean_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "latin_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "ta_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "te_PP-OCRv3_mobile_rec": ["onnxruntime", "paddle"], "PP-LCNet_x0_25_textline_ori": ["onnxruntime", "paddle"], "AutoEncoder_ad": ["onnxruntime", "paddle"], "DLinear_ad": ["onnxruntime", "paddle"], "DLinear": ["onnxruntime", "paddle"], "NLinear": ["onnxruntime", "paddle"], "RLinear": ["onnxruntime", "paddle"], "PP-LCNet_x1_0_vehicle_attribute": ["onnxruntime", "paddle"], "PP-TSM-R50_8frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_16frames_uniform": ["paddle", "onnxruntime"], "PP-TSMv2-LCNetV2_8frames_uniform": ["paddle", "onnxruntime"], "DETR-R50": ["paddle", "onnxruntime"], "BlazeFace-FPN-SSH": ["paddle"], "BlazeFace": ["paddle"], "PP-YOLOE_plus-S_face": ["paddle", "onnxruntime"], "PP-FormulaNet-S": ["paddle"], "PP-YOLOE-L_human": ["onnxruntime", "paddle"], "PP-YOLOE-S_human": ["onnxruntime", "paddle"], "STFPM": ["paddle", "onnxruntime"], "SwinTransformer_base_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_base_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window12_384": ["paddle", "onnxruntime"], "SwinTransformer_large_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_small_patch4_window7_224": ["paddle", "onnxruntime"], "SwinTransformer_tiny_patch4_window7_224": ["paddle", "onnxruntime"], "Cascade-MaskRCNN-ResNet50-FPN": ["paddle"], "Cascade-MaskRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "MaskRCNN-ResNeXt101-vd-FPN": ["paddle"], "MaskRCNN-ResNet101-FPN": ["paddle"], "MaskRCNN-ResNet101-vd-FPN": ["paddle"], "MaskRCNN-ResNet50-FPN": ["paddle"], "MaskRCNN-ResNet50-vd-FPN": ["paddle"], "MaskRCNN-ResNet50": ["paddle"], "PP-YOLOE_seg-S": ["paddle", "onnxruntime"], "SOLOv2": ["paddle", "onnxruntime"], "PP-DocLayout-M": ["paddle", "onnxruntime"], "PP-DocLayout-S": ["onnxruntime", "paddle"], "PicoDet-L_layout_17cls": ["paddle", "onnxruntime"], "PicoDet-L_layout_3cls": ["paddle", "onnxruntime"], "PicoDet-S_layout_17cls": ["onnxruntime", "paddle"], "PicoDet-S_layout_3cls": ["onnxruntime", "paddle"], "PicoDet_layout_1x_table": ["paddle", "onnxruntime"], "PicoDet_layout_1x": ["paddle", "onnxruntime"], "Cascade-FasterRCNN-ResNet50-FPN": ["paddle"], "Cascade-FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "CenterNet-DLA-34": ["paddle"], "CenterNet-ResNet50": ["paddle"], "FCOS-ResNet50": ["paddle", "onnxruntime"], "FasterRCNN-ResNeXt101-vd-FPN": ["paddle"], "FasterRCNN-ResNet101-FPN": ["paddle"], "FasterRCNN-ResNet101": ["paddle"], "FasterRCNN-ResNet34-FPN": ["paddle"], "FasterRCNN-ResNet50-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-FPN": ["paddle"], "FasterRCNN-ResNet50-vd-SSLDv2-FPN": ["paddle"], "FasterRCNN-ResNet50": ["paddle"], "FasterRCNN-Swin-Tiny-FPN": ["paddle"], "PP-YOLOE_plus-L": ["paddle", "onnxruntime"], "PP-YOLOE_plus-M": ["paddle", "onnxruntime"], "PP-YOLOE_plus-S": ["paddle", "onnxruntime"], "PP-YOLOE_plus-X": ["paddle", "onnxruntime"], "PicoDet-L": ["paddle", "onnxruntime"], "PicoDet-M": ["paddle", "onnxruntime"], "PicoDet-S": ["paddle", "onnxruntime"], "PicoDet-XS": ["paddle", "onnxruntime"], "YOLOX-L": ["paddle", "onnxruntime"], "YOLOX-M": ["onnxruntime", "paddle"], "YOLOX-N": ["onnxruntime", "paddle"], "YOLOX-S": ["onnxruntime", "paddle"], "YOLOX-T": ["onnxruntime", "paddle"], "YOLOX-X": ["paddle", "onnxruntime"], "YOLOv3-DarkNet53": ["paddle"], "YOLOv3-MobileNetV3": ["paddle"], "YOLOv3-ResNet50_vd_DCN": ["paddle"], "PP-YOLOE-R-L": ["paddle"], "PP-LiteSeg-B": ["paddle"], "PP-LiteSeg-T": ["paddle"], "SegFormer-B0": ["paddle", "onnxruntime"], "SegFormer-B1": ["paddle", "onnxruntime"], "SegFormer-B2": ["onnxruntime", "paddle"], "SegFormer-B3": ["onnxruntime", "paddle"], "SegFormer-B4": ["onnxruntime", "paddle"], "SegFormer-B5": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-L": ["onnxruntime", "paddle"], "PP-YOLOE_plus_SOD-S": ["onnxruntime", "paddle"], "SLANeXt_wired": ["paddle", "onnxruntime"], "SLANeXt_wireless": ["paddle", "onnxruntime"], "SLANet_plus": ["paddle", "onnxruntime"], "SLANet": ["paddle", "onnxruntime"], "Nonstationary_ad": ["onnxruntime", "paddle"], "PatchTST_ad": ["onnxruntime", "paddle"], "TimesNet_ad": ["onnxruntime", "paddle"], "TimesNet_cls": ["paddle", "onnxruntime"], "Nonstationary": ["onnxruntime", "paddle"], "PatchTST": ["onnxruntime", "paddle"], "TimesNet": ["paddle", "onnxruntime"], "PP-YOLOE-L_vehicle": ["paddle", "onnxruntime"], "PP-YOLOE-S_vehicle": ["onnxruntime", "paddle"], "PP-FormulaNet-L": ["paddle"], "PP-YOLOE_plus_SOD-largesize-L": ["onnxruntime", "paddle"], "LaTeX_OCR_rec": ["paddle"], "UniMERNet": ["paddle"], "Mask-RT-DETR-H": ["paddle", "onnxruntime"], "Mask-RT-DETR-L": ["paddle", "onnxruntime"], "Mask-RT-DETR-M": ["paddle", "onnxruntime"], "Mask-RT-DETR-S": ["paddle", "onnxruntime"], "Mask-RT-DETR-X": ["paddle", "onnxruntime"], "Co-DINO-R50": ["paddle"], "Co-DINO-Swin-L": ["paddle"], "Co-Deformable-DETR-R50": ["paddle"], "Co-Deformable-DETR-Swin-T": ["paddle"], "MaskFormer_small": ["paddle", "onnxruntime"], "MaskFormer_tiny": ["paddle", "onnxruntime"], "TiDE": ["paddle"], "PP-DocBlockLayout": ["paddle", "onnxruntime"], "PP-DocLayout_plus-L": ["paddle", "onnxruntime"], "PP-OCRv5_server_rec": ["onnxruntime", "paddle"], "PP-OCRv5_mobile_rec": ["onnxruntime", "paddle"], "PP-OCRv5_server_det": ["onnxruntime", "paddle"], "PP-OCRv5_mobile_det": ["paddle", "onnxruntime"], "PP-FormulaNet_plus-L": ["paddle"], "PP-FormulaNet_plus-M": ["paddle"], "PP-FormulaNet_plus-S": ["paddle"], "YOLO-Worldv2-L": ["paddle"], "SAM-H_box": ["paddle"], "PP-LCNet_x1_0_textline_ori": ["onnxruntime", "paddle"], "GroundingDINO-T": ["paddle"], "SAM-H_point": ["paddle"], "BEVFusion": ["paddle"], "YOWO": ["paddle"]}}}