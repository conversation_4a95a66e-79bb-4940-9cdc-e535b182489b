# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

STATIC_SHAPE_MODEL_LIST = [
    "CenterNet-DLA-34",
    "CenterNet-ResNet50",
    "Co-Deformable-DETR-Swin-T",
    "Co-DINO-Swin-L",
    "FasterRCNN-Swin-Tiny-FPN",
    "Mask-RT-DETR-H",
    "Mask-RT-DETR-L",
    "Mask-RT-DETR-M",
    "Mask-RT-DETR-S",
    "Mask-RT-DETR-X",
    "PicoDet_layout_1x_table",
    "PicoDet_layout_1x",
    "PicoDet-L_layout_17cls",
    "PicoDet-L_layout_3cls",
    "PicoDet-L",
    "PicoDet-M",
    "PicoDet-S_layout_17cls",
    "PicoDet-S_layout_3cls",
    "PicoDet-S",
    "PicoDet-XS",
    "PP-ShiTuV2_det",
    "PP-YOLOE-L_human",
    "PP-YOLOE-L_vehicle",
    "PP-YOLOE_plus-L",
    "PP-YOLOE_plus-M",
    "PP-YOLOE_plus_SOD-largesize-L",
    "PP-YOLOE_plus_SOD-L",
    "PP-YOLOE_plus_SOD-S",
    "PP-YOLOE_plus-S",
    "PP-YOLOE_plus-X",
    "PP-YOLOE_seg-S",
    "PP-YOLOE-S_human",
    "PP-YOLOE-S_vehicle",
    "RT-DETR-H_layout_17cls",
    "RT-DETR-H_layout_3cls",
    "RT-DETR-H",
    "RT-DETR-L",
    "RT-DETR-R18",
    "RT-DETR-R50",
    "RT-DETR-X",
    "YOLOv3-DarkNet53",
    "YOLOv3-MobileNetV3",
    "YOLOv3-ResNet50_vd_DCN",
    "YOLOX-L",
    "YOLOX-M",
    "YOLOX-N",
    "YOLOX-S",
    "YOLOX-T",
    "YOLOX-X",
    "PP-DocLayout-L",
    "PP-DocLayout-M",
    "PP-DocLayout-S",
    "PP-DocLayout_plus-L",
    "PP-DocBlockLayout",
]
