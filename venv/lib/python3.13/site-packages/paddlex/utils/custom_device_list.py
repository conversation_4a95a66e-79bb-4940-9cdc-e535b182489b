# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

DCU_WHITELIST = [
    "ResNet18",
    "ResNet34",
    "ResNet50",
    "ResNet101",
    "ResNet152",
    "PP-LCNet_x1_0",
    "PP-HGNetV2-B0_ML",
    "PP-HGNetV2-B4_ML",
    "PP-HGNetV2-B6_ML",
    "CLIP_vit_base_patch16_224_ML",
    "PP-ShiTuV2_rec_CLIP_vit_base",
    "PP-YOLOE_plus-L",
    "PP-YOLOE_plus-M",
    "PP-YOLOE_plus-S",
    "RT-DETR-R18",
    "PicoDet-L",
    "PicoDet-M",
    "PicoDet-S",
    "PicoDet-XS",
    "FCOS-ResNet50",
    "YOLOX-N",
    "FasterRCNN-ResNet34-FPN",
    "YOLOv3-DarkNet53",
    "Cascade-FasterRCNN-ResNet50-FPN",
    "PP-YOLOE_plus_SOD-S",
    "PP-YOLOE_plus_SOD-L",
    "PP-YOLOE_plus_SOD-largesize-L",
    "STFPM",
    "Deeplabv3_Plus-R50",
    "Deeplabv3_Plus-R101",
    "PP-LiteSeg-T",
    "PP-OCRv4_server_rec",
    "PP-OCRv4_server_det",
    "PP-OCRv4_mobile_det",
    "DLinear",
    "RLinear",
    "NLinear",
    "PicoDet_LCNet_x2_5_face",
]

MLU_WHITELIST = [
    "ResNet18",
    "ResNet34",
    "ResNet50",
    "ResNet101",
    "ResNet152",
    "ResNet18_vd",
    "ResNet34_vd",
    "ResNet50_vd",
    "ResNet101_vd",
    "ResNet152_vd",
    "ResNet200_vd",
    "PP-LCNet_x0_25",
    "PP-LCNet_x0_35",
    "PP-LCNet_x0_5",
    "PP-LCNet_x0_75",
    "PP-LCNet_x1_0",
    "PP-LCNet_x1_5",
    "PP-LCNet_x2_5",
    "PP-LCNet_x2_0",
    "MobileNetV3_large_x0_35",
    "MobileNetV3_large_x0_5",
    "MobileNetV3_large_x0_75",
    "MobileNetV3_large_x1_0",
    "MobileNetV3_large_x1_25",
    "MobileNetV3_small_x0_35",
    "MobileNetV3_small_x0_5",
    "MobileNetV3_small_x0_75",
    "MobileNetV3_small_x1_0",
    "MobileNetV3_small_x1_25",
    "PP-HGNet_small",
    "PP-HGNet_tiny",
    "PP-HGNet_base",
    "PP-ShiTuV2_rec_CLIP_vit_base",
    "PP-ShiTuV2_rec_CLIP_vit_large",
    "PP-YOLOE_plus-X",
    "PP-YOLOE_plus-L",
    "PP-YOLOE_plus-M",
    "PP-YOLOE_plus-S",
    "PicoDet-L",
    "PicoDet-M",
    "PicoDet-S",
    "PicoDet-XS",
    "STFPM",
    "PP-LiteSeg-T",
    "PP-OCRv4_server_rec",
    "PP-OCRv4_mobile_rec",
    "PP-OCRv4_server_det",
    "PP-OCRv4_mobile_det",
    "PicoDet_layout_1x",
    "DLinear",
    "RLinear",
    "NLinear",
    "PicoDet_LCNet_x2_5_face",
]

NPU_BLACKLIST = [
    "PP-LiteSeg-B",
    "MaskFormer_tiny",
    "MaskFormer_small",
    "PP-YOLOE_plus-S_face",
    "BlazeFace",
    "BlazeFace-FPN-SSH",
    "MobileFaceNet",
    "ResNet50_face",
    "PP-TSM-R50_8frames_uniform",
    "PP-TSMv2-LCNetV2_8frames_uniform",
    "PP-TSMv2-LCNetV2_16frames_uniform",
    "YOWO",
    "PP-YOLOE-R-L",
    "SAM-H-BOX",
    "SAM-H-point",
    "GroundingDINO-T",
    "PP-TinyPose128x96",
    "PP-TinyPose256x192",
    "BEVFusion",
    "whisper_large",
    "whisper_medium",
    "whisper_small",
    "whisper_base",
    "whisper_tiny",
    "UniMERNet",
]

XPU_WHITELIST = [
    "ResNet18",
    "ResNet34",
    "ResNet50",
    "ResNet101",
    "ResNet152",
    "ResNet18_vd",
    "ResNet34_vd",
    "ResNet50_vd",
    "ResNet101_vd",
    "ResNet152_vd",
    "ResNet200_vd",
    "PP-LCNet_x0_25",
    "PP-LCNet_x0_35",
    "PP-LCNet_x0_5",
    "PP-LCNet_x0_75",
    "PP-LCNet_x1_0",
    "PP-LCNet_x1_5",
    "PP-LCNet_x2_5",
    "PP-LCNet_x2_0",
    "MobileNetV3_large_x0_35",
    "MobileNetV3_large_x0_5",
    "MobileNetV3_large_x0_75",
    "MobileNetV3_large_x1_0",
    "MobileNetV3_large_x1_25",
    "MobileNetV3_small_x0_35",
    "MobileNetV3_small_x0_5",
    "MobileNetV3_small_x0_75",
    "MobileNetV3_small_x1_0",
    "MobileNetV3_small_x1_25",
    "PP-HGNet_small",
    "PP-HGNet_tiny",
    "PP-HGNet_base",
    "PP-YOLOE_plus-X",
    "PP-YOLOE_plus-L",
    "PP-YOLOE_plus-M",
    "PP-YOLOE_plus-S",
    "PicoDet-L",
    "PicoDet-M",
    "PicoDet-S",
    "PicoDet-XS",
    "STFPM",
    "PP-LiteSeg-T",
    "PP-OCRv4_server_rec",
    "PP-OCRv4_mobile_rec",
    "PP-OCRv4_server_det",
    "PP-OCRv4_mobile_det",
    "PicoDet_layout_1x",
    "DLinear",
    "RLinear",
    "NLinear",
    "PicoDet_LCNet_x2_5_face",
    "PP-LCNet_x1_0_doc_ori",
    "UVDoc",
    "PP-DocLayout-L",
    "PP-DocLayout-M",
    "PP-DocLayout-S",
    "PP-DocLayout_plus-L",
    "PP-DocBlockLayout",
    "SLANeXt_wired",
    "SLANeXt_wireless",
    "PP-LCNet_x1_0_table_cls",
    "RT-DETR-L_wired_table_cell_det",
    "RT-DETR-L_wireless_table_cell_det",
    "PP-OCRv4_server_seal_det",
    "PP-OCRv4_mobile_seal_det",
    "PP-OCRv5_server_rec",
    "PP-OCRv5_mobile_rec",
    "PPDocBee-2B",
    "PPDocBee-7B",
    "PP-FormulaNet-S",
    "PP-FormulaNet-L",
    "PP-FormulaNet-M",
    "PP-FormulaNet_plus-M",
    "PP-FormulaNet_plus-L",
    "PP-OCRv5_server_det",
    "PP-OCRv5_mobile_det",
    "PP-Chart2Table",
]

GCU_WHITELIST = [
    "ConvNeXt_base_224",
    "ConvNeXt_base_384",
    "ConvNeXt_large_224",
    "ConvNeXt_large_384",
    "ConvNeXt_small",
    "ConvNeXt_tiny",
    "FasterNet-L",
    "FasterNet-M",
    "FasterNet-S",
    "FasterNet-T0",
    "FasterNet-T1",
    "FasterNet-T2",
    "MobileNetV1_x0_25",
    "MobileNetV1_x0_5",
    "MobileNetV1_x0_75",
    "MobileNetV1_x1_0",
    "MobileNetV2_x0_25",
    "MobileNetV2_x0_5",
    "MobileNetV2_x1_0",
    "MobileNetV2_x1_5",
    "MobileNetV2_x2_0",
    "MobileNetV3_large_x0_35",
    "MobileNetV3_large_x0_5",
    "MobileNetV3_large_x0_75",
    "MobileNetV3_large_x1_0",
    "MobileNetV3_large_x1_25",
    "MobileNetV3_small_x0_35",
    "MobileNetV3_small_x0_5",
    "MobileNetV3_small_x0_75",
    "MobileNetV3_small_x1_0",
    "MobileNetV3_small_x1_25",
    "MobileNetV4_conv_large",
    "MobileNetV4_conv_medium",
    "MobileNetV4_conv_small",
    "PP-HGNet_base",
    "PP-HGNet_small",
    "PP-HGNet_tiny",
    "PP-HGNetV2-B0",
    "PP-HGNetV2-B1",
    "PP-HGNetV2-B2",
    "PP-HGNetV2-B3",
    "PP-HGNetV2-B4",
    "PP-HGNetV2-B5",
    "PP-HGNetV2-B6",
    "PP-LCNet_x0_25",
    "PP-LCNet_x0_35",
    "PP-LCNet_x0_5",
    "PP-LCNet_x0_75",
    "PP-LCNet_x1_0",
    "PP-LCNet_x1_5",
    "PP-LCNet_x2_0",
    "PP-LCNet_x2_5",
    "PP-LCNetV2_base",
    "PP-LCNetV2_large",
    "PP-LCNetV2_small",
    "ResNet18_vd",
    "ResNet18",
    "ResNet34_vd",
    "ResNet34",
    "ResNet50_vd",
    "ResNet50",
    "ResNet101_vd",
    "ResNet101",
    "ResNet152_vd",
    "ResNet152",
    "ResNet200_vd",
    "StarNet-S1",
    "StarNet-S2",
    "StarNet-S3",
    "StarNet-S4",
    "FCOS-ResNet50",
    "PicoDet-L",
    "PicoDet-M",
    "PicoDet-S",
    "PicoDet-XS",
    "PP-YOLOE_plus-L",
    "PP-YOLOE_plus-M",
    "PP-YOLOE_plus-S",
    "PP-YOLOE_plus-X",
    "RT-DETR-H",
    "RT-DETR-L",
    "RT-DETR-R18",
    "RT-DETR-R50",
    "RT-DETR-X",
    "PP-YOLOE-L_human",
    "PP-YOLOE-S_human",
    "PP-OCRv4_mobile_det",
    "PP-OCRv4_server_det",
    "PP-OCRv4_mobile_rec",
    "PP-OCRv4_server_rec",
]
