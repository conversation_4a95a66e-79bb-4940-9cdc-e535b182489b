# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from functools import wraps

from . import logging


class FuncRegister(object):
    def __init__(self, register_map):
        assert isinstance(register_map, dict)
        self._register_map = register_map

    def __call__(self, key=None):
        """register the decoratored func as key in dict"""

        def decorator(func):
            actual_key = key if key is not None else func.__name__
            self._register_map[actual_key] = func
            logging.debug(
                f"The func ({func.__name__}) has been registered as key ({actual_key})."
            )

            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            return wrapper

        return decorator
