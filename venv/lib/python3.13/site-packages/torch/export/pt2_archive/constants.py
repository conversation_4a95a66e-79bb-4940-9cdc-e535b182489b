# Defined in torch/csrc/export/pt2_archive_constants.h
from torch._C._export import pt2_archive_constants


AOTINDUCTOR_DIR: str = pt2_archive_constants.AOTINDUCTOR_DIR
ARCHIVE_FORMAT_PATH: str = pt2_archive_constants.ARCHIVE_FORMAT_PATH
ARCHIVE_FORMAT_VALUE: str = pt2_archive_constants.ARCHIVE_FORMAT_VALUE
ARCHIVE_ROOT_NAME: str = pt2_archive_constants.ARCHIVE_ROOT_NAME
ARCHIVE_VERSION_PATH: str = pt2_archive_constants.ARCHIVE_VERSION_PATH
ARCHIVE_VERSION_VALUE: str = pt2_archive_constants.ARCHIVE_VERSION_VALUE
CONSTANTS_DIR: str = pt2_archive_constants.CONSTANTS_DIR
CUSTOM_OBJ_FILENAME_PREFIX: str = pt2_archive_constants.CUSTOM_OBJ_FILENAME_PREFIX
EXTRA_DIR: str = pt2_archive_constants.EXTRA_DIR
MODELS_DIR: str = pt2_archive_constants.MODELS_DIR
MODELS_FILENAME_FORMAT: str = pt2_archive_constants.MODELS_FILENAME_FORMAT
MODULE_INFO_PATH: str = pt2_archive_constants.MODULE_INFO_PATH
MTIA_DIR: str = pt2_archive_constants.MTIA_DIR
SAMPLE_INPUTS_DIR: str = pt2_archive_constants.SAMPLE_INPUTS_DIR
SAMPLE_INPUTS_FILENAME_FORMAT: str = pt2_archive_constants.SAMPLE_INPUTS_FILENAME_FORMAT
TENSOR_CONSTANT_FILENAME_PREFIX: str = (
    pt2_archive_constants.TENSOR_CONSTANT_FILENAME_PREFIX
)
WEIGHT_FILENAME_PREFIX: str = pt2_archive_constants.WEIGHT_FILENAME_PREFIX
WEIGHTS_DIR: str = pt2_archive_constants.WEIGHTS_DIR
XL_MODEL_WEIGHTS_DIR: str = pt2_archive_constants.XL_MODEL_WEIGHTS_DIR
XL_MODEL_WEIGHTS_PARAM_CONFIG_PATH: str = (
    pt2_archive_constants.XL_MODEL_WEIGHTS_PARAM_CONFIG_PATH
)
