2025-08-28 13:50:49 | INFO     | src.utils.logger:setup_logging:47 - Logging initialized
2025-08-28 13:50:49 | INFO     | src.app:__init__:30 - Starting Image Transcriber application
2025-08-28 13:50:49 | INFO     | src.gui.main_window:_setup_window:55 - Main window initialized
2025-08-28 13:50:49 | INFO     | src.gui.main_window:_create_layout:106 - Layout created with resizable panels
2025-08-28 13:50:49 | INFO     | src.gui.main_window:_setup_menu:156 - Menu bar created
2025-08-28 13:50:50 | INFO     | src.ocr.tesseract_engine:_check_availability:32 - Tesseract 5.5.1 is available
2025-08-28 13:50:50 | INFO     | src.ocr.manager:_initialize_engines:31 - Tesseract engine initialized
2025-08-28 13:50:52 | INFO     | src.ocr.easyocr_engine:_check_availability:26 - EasyOCR is available
2025-08-28 13:50:52 | INFO     | src.ocr.manager:_initialize_engines:37 - EasyOCR engine initialized
2025-08-28 13:50:52 | INFO     | src.ocr.manager:_initialize_engines:42 - Default OCR engine set to: tesseract
2025-08-28 13:50:52 | INFO     | src.gui.file_browser:_create_widgets:119 - File browser panel created
2025-08-28 13:50:52 | INFO     | src.gui.image_canvas:_create_widgets:132 - Image canvas created
2025-08-28 13:50:53 | INFO     | src.gui.settings_panel:_create_widgets:162 - Settings panel created
2025-08-28 13:50:53 | INFO     | src.gui.log_panel:_create_widgets:114 - Log panel created
2025-08-28 13:50:53 | INFO     | src.app:_setup_panels:69 - GUI panels initialized
2025-08-28 13:50:53 | INFO     | src.app:_register_callbacks:99 - Callbacks registered
2025-08-28 13:50:53 | INFO     | src.app:__init__:46 - Application initialized successfully
2025-08-28 13:50:53 | INFO     | src.gui.main_window:run:246 - Starting application main loop
2025-08-28 13:54:00 | INFO     | src.app:_new_project:130 - Created new project: Martial Emperor
2025-08-28 13:55:23 | INFO     | src.gui.file_browser:_import_image_files:192 - Imported image: 0.jpg
2025-08-28 13:55:23 | INFO     | src.app:_save_project:174 - Saved project: Martial Emperor
2025-08-28 13:55:29 | INFO     | src.gui.image_canvas:load_image:194 - Loaded image: 0.jpg
2025-08-28 13:55:29 | INFO     | src.app:_on_image_opened:241 - Opened image: 0.jpg
2025-08-28 13:55:39 | INFO     | src.gui.image_canvas:load_image:194 - Loaded image: 0.jpg
2025-08-28 13:55:40 | INFO     | src.gui.image_canvas:load_image:194 - Loaded image: 0.jpg
2025-08-28 14:04:08 | INFO     | src.utils.logger:setup_logging:47 - Logging initialized
2025-08-28 14:04:08 | INFO     | src.app:__init__:30 - Starting Image Transcriber application
2025-08-28 14:04:08 | INFO     | src.gui.main_window:_setup_window:55 - Main window initialized
2025-08-28 14:04:08 | INFO     | src.gui.main_window:_create_layout:106 - Layout created with resizable panels
2025-08-28 14:04:08 | INFO     | src.gui.main_window:_setup_menu:164 - Menu bar created
2025-08-28 14:04:08 | INFO     | src.ocr.tesseract_engine:_check_availability:32 - Tesseract 5.5.1 is available
2025-08-28 14:04:08 | INFO     | src.ocr.manager:_initialize_engines:31 - Tesseract engine initialized
2025-08-28 14:04:10 | INFO     | src.ocr.easyocr_engine:_check_availability:26 - EasyOCR is available
2025-08-28 14:04:10 | INFO     | src.ocr.manager:_initialize_engines:37 - EasyOCR engine initialized
2025-08-28 14:04:10 | INFO     | src.ocr.manager:_initialize_engines:42 - Default OCR engine set to: tesseract
2025-08-28 14:04:10 | INFO     | src.gui.file_browser:_create_widgets:119 - File browser panel created
2025-08-28 14:04:10 | INFO     | src.gui.image_canvas:_create_widgets:179 - Image canvas created
2025-08-28 14:04:11 | INFO     | src.gui.settings_panel:_create_widgets:162 - Settings panel created
2025-08-28 14:04:11 | INFO     | src.gui.log_panel:_create_widgets:114 - Log panel created
2025-08-28 14:04:11 | INFO     | src.app:_setup_panels:69 - GUI panels initialized
2025-08-28 14:04:11 | INFO     | src.app:_register_callbacks:103 - Callbacks registered
2025-08-28 14:04:11 | INFO     | src.app:__init__:46 - Application initialized successfully
2025-08-28 14:04:11 | INFO     | src.gui.main_window:run:270 - Starting application main loop
2025-08-28 14:04:31 | INFO     | src.app:_open_project:160 - Opened project: Martial Emperor
2025-08-28 14:04:40 | INFO     | src.gui.image_canvas:load_image:217 - Loaded image: 0.jpg
2025-08-28 14:04:40 | INFO     | src.app:_on_image_opened:245 - Opened image: 0.jpg
2025-08-28 14:06:08 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:06:08 | INFO     | src.gui.image_canvas:_on_canvas_release:471 - Created drawing: a4a976a5-bc44-4ffd-8ca6-be326495cfa4
2025-08-28 14:06:19 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:06:19 | INFO     | src.gui.image_canvas:_clear_drawings:309 - Cleared all drawings
2025-08-28 14:06:28 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:06:28 | INFO     | src.gui.image_canvas:_on_canvas_release:471 - Created drawing: 58f96027-3ee3-4be7-9a33-b263febabd4a
2025-08-28 14:06:40 | INFO     | src.ocr.manager:extract_text_from_region:92 - Extracted text using tesseract: '...'
2025-08-28 14:06:40 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:06:40 | INFO     | src.app:_on_transcription_generated:360 - Generated transcription: a1946c1b-8069-4209-96d4-7d04008990be
2025-08-28 14:08:03 | INFO     | src.ocr.manager:extract_text_from_region:92 - Extracted text using tesseract: '...'
2025-08-28 14:08:03 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:08:03 | INFO     | src.app:_on_transcription_generated:360 - Generated transcription: 08a2e898-a68b-4483-8449-cd86b1d98616
2025-08-28 14:09:30 | INFO     | src.ocr.manager:extract_text_from_region:92 - Extracted text using tesseract: '...'
2025-08-28 14:09:30 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:09:30 | INFO     | src.app:_on_transcription_generated:360 - Generated transcription: e58a1c6e-5857-4a7b-aa65-ff76358b6cf9
2025-08-28 14:13:26 | INFO     | src.app:_save_project:178 - Saved project: Martial Emperor
2025-08-28 14:13:26 | INFO     | src.gui.main_window:_on_closing:257 - Application closing
2025-08-28 14:13:27 | INFO     | src.app:run:423 - Application shutting down
