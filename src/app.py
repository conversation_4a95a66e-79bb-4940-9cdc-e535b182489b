"""Main application controller."""

import tkinter as tk
from tkinter import messagebox, filedialog, simpledialog
from pathlib import Path
from typing import Optional
import threading

from src.gui.main_window import MainWindow
from src.gui.file_browser import FileBrowserPanel
from src.gui.image_canvas import ImageCan<PERSON>
from src.gui.settings_panel import SettingsPanel
from src.gui.log_panel import LogPanel
from src.models.project import Project, ImageFile, BoundingBox
from src.ocr.manager import OCRManager
from src.config.settings import settings
from src.utils.logger import get_logger, setup_logging
from src.utils.crash_handler import crash_handler
from src.utils.error_handler import handle_exceptions, error_reporter

logger = get_logger(__name__)


class ImageTranscriberApp:
    """Main application controller."""
    
    def __init__(self):
        # Initialize logging
        setup_logging()
        logger.info("Starting Image Transcriber application")

        # Check for crash recovery
        if crash_handler.check_for_recovery():
            logger.warning("Previous crash detected - recovery mode available")
        
        # Initialize components
        self.main_window = MainWindow()
        self.ocr_manager = OCRManager()
        self.current_project: Optional[Project] = None
        self.current_image: Optional[ImageFile] = None
        
        # Initialize GUI panels
        self._setup_panels()
        self._register_callbacks()
        
        logger.info("Application initialized successfully")

        # Clear recovery data on successful startup
        crash_handler.clear_recovery_data()
    
    def _setup_panels(self):
        """Setup GUI panels."""
        # File browser panel
        left_panel = self.main_window.get_panel('left')
        self.file_browser = FileBrowserPanel(left_panel)
        
        # Image canvas panel
        main_panel = self.main_window.get_panel('main')
        self.image_canvas = ImageCanvas(main_panel)
        
        # Settings panel
        right_panel = self.main_window.get_panel('right')
        self.settings_panel = SettingsPanel(right_panel, self.ocr_manager)

        # Log panel
        bottom_panel = self.main_window.get_panel('bottom')
        self.log_panel = LogPanel(bottom_panel)
        
        logger.info("GUI panels initialized")
    
    def _register_callbacks(self):
        """Register callbacks between components."""
        # Main window callbacks
        self.main_window.register_callback('new_project', self._new_project)
        self.main_window.register_callback('open_project', self._open_project)
        self.main_window.register_callback('save_project', self._save_project)
        self.main_window.register_callback('save_project_as', self._save_project_as)
        self.main_window.register_callback('import_images', self._import_images)
        self.main_window.register_callback('generate_transcription', self._generate_transcription)
        self.main_window.register_callback('zoom_in', self._zoom_in)
        self.main_window.register_callback('zoom_out', self._zoom_out)
        self.main_window.register_callback('fit_to_window', self._fit_to_window)
        self.main_window.register_callback('actual_size', self._actual_size)
        
        # File browser callbacks
        self.file_browser.register_callback('new_project', self._new_project)
        self.file_browser.register_callback('open_project', self._open_project)
        self.file_browser.register_callback('image_selected', self._on_image_selected)
        self.file_browser.register_callback('image_opened', self._on_image_opened)
        self.file_browser.register_callback('project_modified', self._on_project_modified)
        
        # Image canvas callbacks
        self.image_canvas.register_callback('image_modified', self._on_image_modified)
        self.image_canvas.register_callback('drawing_created', self._on_drawing_created)
        self.image_canvas.register_callback('generate_transcription', self._generate_transcription_for_region)
        
        # Settings panel callbacks
        self.settings_panel.register_callback('generate_transcription', self._generate_transcription_with_settings)
        self.settings_panel.register_callback('transcription_selected', self._on_transcription_selected)
        self.settings_panel.register_callback('transcription_edited', self._on_transcription_edited)
        self.settings_panel.register_callback('transcription_deleted', self._on_transcription_deleted)
        
        logger.info("Callbacks registered")
    
    def _new_project(self):
        """Create a new project."""
        try:
            # Get project details from user
            project_name = simpledialog.askstring("New Project", "Enter project name:")
            if not project_name:
                return
            
            # Select project directory
            project_dir = filedialog.askdirectory(title="Select Project Directory")
            if not project_dir:
                return
            
            # Create project directory
            project_path = Path(project_dir) / project_name
            project_path.mkdir(exist_ok=True)
            
            # Create project
            project = Project(
                name=project_name,
                path=str(project_path)
            )
            
            # Save project
            project.save()
            
            # Set as current project
            self._set_current_project(project)
            
            logger.info(f"Created new project: {project_name}")
            messagebox.showinfo("Success", f"Project '{project_name}' created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create project: {e}")
            messagebox.showerror("Error", f"Failed to create project: {e}")
    
    def _open_project(self):
        """Open an existing project."""
        try:
            # Select project file
            file_path = filedialog.askopenfilename(
                title="Open Project",
                filetypes=[("Project files", "project.json"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            # Load project
            project_dir = Path(file_path).parent
            project = Project.load(str(project_dir))
            
            # Set as current project
            self._set_current_project(project)
            
            logger.info(f"Opened project: {project.name}")
            messagebox.showinfo("Success", f"Project '{project.name}' opened successfully")
            
        except Exception as e:
            logger.error(f"Failed to open project: {e}")
            messagebox.showerror("Error", f"Failed to open project: {e}")
    
    def _save_project(self, project: Optional[Project] = None):
        """Save the current project."""
        try:
            if project is None:
                project = self.current_project
            
            if project is None:
                messagebox.showwarning("Warning", "No project to save")
                return
            
            project.save()
            logger.info(f"Saved project: {project.name}")
            
        except Exception as e:
            logger.error(f"Failed to save project: {e}")
            messagebox.showerror("Error", f"Failed to save project: {e}")
    
    def _save_project_as(self, project: Project):
        """Save project with a new name."""
        try:
            # Get new project name
            new_name = simpledialog.askstring("Save As", "Enter new project name:", initialvalue=project.name)
            if not new_name:
                return
            
            # Select directory
            project_dir = filedialog.askdirectory(title="Select Directory for New Project")
            if not project_dir:
                return
            
            # Create new project directory
            new_project_path = Path(project_dir) / new_name
            new_project_path.mkdir(exist_ok=True)
            
            # Update project
            project.name = new_name
            project.path = str(new_project_path)
            
            # Save project
            project.save()
            
            # Update UI
            self._set_current_project(project)
            
            logger.info(f"Saved project as: {new_name}")
            messagebox.showinfo("Success", f"Project saved as '{new_name}'")
            
        except Exception as e:
            logger.error(f"Failed to save project as: {e}")
            messagebox.showerror("Error", f"Failed to save project: {e}")
    
    def _import_images(self, project: Project):
        """Import images into project."""
        self.file_browser._import_images()
    
    def _set_current_project(self, project: Optional[Project]):
        """Set the current project."""
        self.current_project = project
        self.main_window.set_current_project(project)
        self.file_browser.set_project(project)
        
        # Clear current image
        self.current_image = None
    
    def _on_image_selected(self, image_file: ImageFile):
        """Handle image selection."""
        self.current_image = image_file
        logger.debug(f"Selected image: {image_file.name}")
    
    def _on_image_opened(self, image_file: ImageFile):
        """Handle image opening."""
        try:
            self.current_image = image_file
            self.image_canvas.load_image(image_file)
            
            # Update transcriptions in settings panel
            self.settings_panel.update_transcriptions(image_file.transcriptions)
            
            logger.info(f"Opened image: {image_file.name}")
            
        except Exception as e:
            logger.error(f"Failed to open image: {e}")
            messagebox.showerror("Error", f"Failed to open image: {e}")
    
    def _on_image_modified(self, image_file: ImageFile):
        """Handle image modification."""
        if self.current_project:
            self._save_project()
    
    def _on_project_modified(self, project: Project):
        """Handle project modification."""
        self._save_project(project)
    
    def _on_drawing_created(self, drawing):
        """Handle drawing creation."""
        logger.debug(f"Drawing created: {drawing.id}")
    
    def _generate_transcription(self):
        """Generate transcription for current selection."""
        if not self.current_image:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        # Use current settings
        settings = self.settings_panel.get_current_settings()
        self._generate_transcription_with_settings(settings)
    
    def _generate_transcription_with_settings(self, ocr_settings):
        """Generate transcription with specific settings."""
        if not self.current_image:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        if not self.current_image.drawings:
            messagebox.showwarning("Warning", "Please draw a region on the image first")
            return
        
        # Use the last drawing as the region
        last_drawing = self.current_image.drawings[-1]
        if last_drawing.type == "rectangle" and len(last_drawing.points) >= 2:
            x1, y1 = last_drawing.points[0]
            x2, y2 = last_drawing.points[1]
            
            bbox = BoundingBox(
                x=min(x1, x2),
                y=min(y1, y2),
                width=abs(x2 - x1),
                height=abs(y2 - y1)
            )
            
            self._generate_transcription_for_region(self.current_image, bbox, ocr_settings)
    
    def _generate_transcription_for_region(self, image_file: ImageFile, bbox: BoundingBox, 
                                         ocr_settings: Optional[dict] = None):
        """Generate transcription for a specific region."""
        try:
            if ocr_settings is None:
                ocr_settings = self.settings_panel.get_current_settings()
            
            # Show progress
            progress_window = tk.Toplevel(self.main_window.root)
            progress_window.title("Generating Transcription")
            progress_window.geometry("300x100")
            progress_window.transient(self.main_window.root)
            progress_window.grab_set()
            
            progress_label = tk.Label(progress_window, text="Processing image...")
            progress_label.pack(pady=20)
            
            progress_bar = tk.ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10)
            progress_bar.start()
            
            def generate_in_thread():
                try:
                    # Generate transcription
                    transcription = self.ocr_manager.extract_text_from_region(
                        image_file, bbox,
                        engine_name=ocr_settings.get('engine'),
                        language=ocr_settings.get('language', 'en'),
                        **{k: v for k, v in ocr_settings.items() if k not in ['engine', 'language']}
                    )
                    
                    # Add to image
                    image_file.add_transcription(transcription)
                    
                    # Update UI on main thread
                    self.main_window.root.after(0, lambda: self._on_transcription_generated(transcription, progress_window))
                    
                except Exception as e:
                    self.main_window.root.after(0, lambda: self._on_transcription_error(e, progress_window))
            
            # Start generation in background thread
            thread = threading.Thread(target=generate_in_thread)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start transcription: {e}")
            messagebox.showerror("Error", f"Failed to start transcription: {e}")
    
    def _on_transcription_generated(self, transcription, progress_window):
        """Handle successful transcription generation."""
        try:
            progress_window.destroy()
            
            # Update UI
            self.settings_panel.update_transcriptions(self.current_image.transcriptions)
            
            # Save project
            if self.current_project:
                self._save_project()
            
            logger.info(f"Generated transcription: {transcription.id}")
            messagebox.showinfo("Success", "Transcription generated successfully")
            
        except Exception as e:
            logger.error(f"Error handling transcription result: {e}")
    
    def _on_transcription_error(self, error, progress_window):
        """Handle transcription generation error."""
        progress_window.destroy()
        logger.error(f"Transcription generation failed: {error}")
        messagebox.showerror("Error", f"Transcription generation failed: {error}")
    
    def _on_transcription_selected(self, transcription_id: str):
        """Handle transcription selection."""
        if self.current_image:
            for transcription in self.current_image.transcriptions:
                if transcription.id == transcription_id:
                    self.settings_panel.load_transcription_text(transcription.final_text)
                    break
    
    def _on_transcription_edited(self, transcription_id: str, edited_text: str):
        """Handle transcription editing."""
        if self.current_image:
            for transcription in self.current_image.transcriptions:
                if transcription.id == transcription_id:
                    transcription.update_text(edited_text)
                    self.settings_panel.update_transcriptions(self.current_image.transcriptions)
                    if self.current_project:
                        self._save_project()
                    break
    
    def _on_transcription_deleted(self, transcription_id: str):
        """Handle transcription deletion."""
        if self.current_image:
            self.current_image.remove_transcription(transcription_id)
            self.settings_panel.update_transcriptions(self.current_image.transcriptions)
            if self.current_project:
                self._save_project()

    def _zoom_in(self):
        """Zoom in on the current image."""
        self.image_canvas._zoom_in()

    def _zoom_out(self):
        """Zoom out on the current image."""
        self.image_canvas._zoom_out()

    def _fit_to_window(self):
        """Fit image to window."""
        self.image_canvas._fit_to_window()

    def _actual_size(self):
        """Show image at actual size (100%)."""
        self.image_canvas._zoom_to_level(1.0)
    
    def run(self):
        """Start the application."""
        try:
            self.main_window.run()
        except Exception as e:
            logger.error(f"Application error: {e}")
            messagebox.showerror("Fatal Error", f"Application error: {e}")
        finally:
            logger.info("Application shutting down")
