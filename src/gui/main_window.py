"""Main application window with resizable panels."""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Optional, Callable
from pathlib import Path

from src.config.settings import settings
from src.models.project import Project
from src.utils.logger import get_logger

logger = get_logger(__name__)


class MainWindow:
    """Main application window with resizable panels."""
    
    def __init__(self):
        self.root = None
        self.current_project: Optional[Project] = None
        self.callbacks = {}
        
        # Panel references
        self.left_panel = None
        self.main_panel = None
        self.right_panel = None
        self.bottom_panel = None
        
        self._setup_window()
        self._create_layout()
        self._setup_menu()
        
    def _setup_window(self):
        """Initialize the main window."""
        # Set appearance mode and color theme
        ctk.set_appearance_mode(settings.appearance_mode)
        ctk.set_default_color_theme(settings.color_theme)
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title(settings.app_name)
        self.root.geometry(f"{settings.window_width}x{settings.window_height}")
        self.root.minsize(settings.min_window_width, settings.min_window_height)
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (settings.window_width // 2)
        y = (self.root.winfo_screenheight() // 2) - (settings.window_height // 2)
        self.root.geometry(f"{settings.window_width}x{settings.window_height}+{x}+{y}")
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        logger.info("Main window initialized")
    
    def _create_layout(self):
        """Create the main layout with resizable panels."""
        # Main container
        main_container = ctk.CTkFrame(self.root)
        main_container.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Top section (left, main, right panels)
        top_frame = ctk.CTkFrame(main_container)
        top_frame.pack(fill="both", expand=True, pady=(0, 5))
        
        # Configure grid weights for resizing
        top_frame.grid_columnconfigure(1, weight=3)  # Main panel gets most space
        top_frame.grid_columnconfigure(0, weight=1)  # Left panel
        top_frame.grid_columnconfigure(2, weight=1)  # Right panel
        top_frame.grid_rowconfigure(0, weight=1)
        
        # Left panel (File browser)
        self.left_panel = ctk.CTkFrame(top_frame)
        self.left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 2))
        
        left_label = ctk.CTkLabel(self.left_panel, text="Project Files", font=ctk.CTkFont(size=16, weight="bold"))
        left_label.pack(pady=10)
        
        # Main panel (Image canvas)
        self.main_panel = ctk.CTkFrame(top_frame)
        self.main_panel.grid(row=0, column=1, sticky="nsew", padx=2)
        
        main_label = ctk.CTkLabel(self.main_panel, text="Image Canvas", font=ctk.CTkFont(size=16, weight="bold"))
        main_label.pack(pady=10)
        
        # Right panel (Settings)
        self.right_panel = ctk.CTkFrame(top_frame)
        self.right_panel.grid(row=0, column=2, sticky="nsew", padx=(2, 0))
        
        right_label = ctk.CTkLabel(self.right_panel, text="OCR Settings", font=ctk.CTkFont(size=16, weight="bold"))
        right_label.pack(pady=10)
        
        # Bottom panel (Logs)
        self.bottom_panel = ctk.CTkFrame(main_container)
        self.bottom_panel.pack(fill="x", pady=(0, 0))
        
        bottom_label = ctk.CTkLabel(self.bottom_panel, text="Logs", font=ctk.CTkFont(size=14, weight="bold"))
        bottom_label.pack(pady=5)
        
        # Set minimum sizes for panels
        self.left_panel.configure(width=250)
        self.right_panel.configure(width=300)
        self.bottom_panel.configure(height=150)
        
        logger.info("Layout created with resizable panels")
    
    def _setup_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.configure(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Project...", command=self._new_project, accelerator="Ctrl+N")
        file_menu.add_command(label="Open Project...", command=self._open_project, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Save Project", command=self._save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="Save Project As...", command=self._save_project_as)
        file_menu.add_separator()
        file_menu.add_command(label="Import Images...", command=self._import_images)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Preferences...", command=self._show_preferences)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self._zoom_in, accelerator="+")
        view_menu.add_command(label="Zoom Out", command=self._zoom_out, accelerator="-")
        view_menu.add_command(label="Fit to Window", command=self._fit_to_window, accelerator="F")
        view_menu.add_command(label="Actual Size", command=self._actual_size, accelerator="1")
        
        # OCR menu
        ocr_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="OCR", menu=ocr_menu)
        ocr_menu.add_command(label="Generate Transcription", command=self._generate_transcription, accelerator="F5")
        ocr_menu.add_separator()
        ocr_menu.add_command(label="OCR Settings...", command=self._show_ocr_settings)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self._new_project())
        self.root.bind('<Control-o>', lambda e: self._open_project())
        self.root.bind('<Control-s>', lambda e: self._save_project())
        self.root.bind('<F5>', lambda e: self._generate_transcription())

        # View shortcuts (these will be handled by the canvas when it has focus)
        self.root.bind('<KeyPress-plus>', lambda e: self._zoom_in())
        self.root.bind('<KeyPress-equal>', lambda e: self._zoom_in())  # + without shift
        self.root.bind('<KeyPress-minus>', lambda e: self._zoom_out())
        self.root.bind('<KeyPress-f>', lambda e: self._fit_to_window())
        self.root.bind('<KeyPress-1>', lambda e: self._actual_size())
        
        logger.info("Menu bar created")
    
    def register_callback(self, event: str, callback: Callable):
        """Register a callback for an event."""
        self.callbacks[event] = callback
    
    def _trigger_callback(self, event: str, *args, **kwargs):
        """Trigger a registered callback."""
        if event in self.callbacks:
            try:
                return self.callbacks[event](*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in callback {event}: {e}")
                messagebox.showerror("Error", f"An error occurred: {e}")
    
    def _new_project(self):
        """Create a new project."""
        self._trigger_callback('new_project')
    
    def _open_project(self):
        """Open an existing project."""
        self._trigger_callback('open_project')
    
    def _save_project(self):
        """Save the current project."""
        if self.current_project:
            self._trigger_callback('save_project', self.current_project)
        else:
            messagebox.showwarning("Warning", "No project to save")
    
    def _save_project_as(self):
        """Save the current project with a new name."""
        if self.current_project:
            self._trigger_callback('save_project_as', self.current_project)
        else:
            messagebox.showwarning("Warning", "No project to save")
    
    def _import_images(self):
        """Import images into the current project."""
        if self.current_project:
            self._trigger_callback('import_images', self.current_project)
        else:
            messagebox.showwarning("Warning", "Please create or open a project first")
    
    def _generate_transcription(self):
        """Generate OCR transcription."""
        self._trigger_callback('generate_transcription')

    def _zoom_in(self):
        """Zoom in on the current image."""
        self._trigger_callback('zoom_in')

    def _zoom_out(self):
        """Zoom out on the current image."""
        self._trigger_callback('zoom_out')

    def _fit_to_window(self):
        """Fit image to window."""
        self._trigger_callback('fit_to_window')

    def _actual_size(self):
        """Show image at actual size (100%)."""
        self._trigger_callback('actual_size')
    
    def _show_preferences(self):
        """Show application preferences."""
        self._trigger_callback('show_preferences')
    
    def _show_ocr_settings(self):
        """Show OCR settings."""
        self._trigger_callback('show_ocr_settings')
    
    def _show_about(self):
        """Show about dialog."""
        messagebox.showinfo(
            "About",
            f"{settings.app_name} v{settings.app_version}\n\n"
            "A modern image transcription application with OCR capabilities.\n\n"
            "Features multiple OCR engines and drawing tools for precise text extraction."
        )
    
    def _on_closing(self):
        """Handle window closing."""
        if self.current_project:
            result = messagebox.askyesnocancel(
                "Save Project",
                "Do you want to save the current project before closing?"
            )
            if result is None:  # Cancel
                return
            elif result:  # Yes
                self._save_project()
        
        logger.info("Application closing")
        self.root.destroy()
    
    def set_current_project(self, project: Optional[Project]):
        """Set the current project."""
        self.current_project = project
        if project:
            self.root.title(f"{settings.app_name} - {project.name}")
        else:
            self.root.title(settings.app_name)
    
    def run(self):
        """Start the application main loop."""
        logger.info("Starting application main loop")
        self.root.mainloop()
    
    def get_panel(self, panel_name: str):
        """Get a reference to a specific panel."""
        panels = {
            'left': self.left_panel,
            'main': self.main_panel,
            'right': self.right_panel,
            'bottom': self.bottom_panel
        }
        return panels.get(panel_name)
