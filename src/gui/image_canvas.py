"""Image canvas for displaying images and drawing crop regions."""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
from typing import Optional, Callable, Tuple
from PIL import Image, ImageTk

from src.models.project import ImageFile, DrawingElement, BoundingBox
from src.config.settings import settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ImageCanvas:
    """Interactive canvas for displaying images and drawing crop regions."""
    
    def __init__(self, parent_frame: ctk.CTkFrame):
        self.parent = parent_frame
        self.current_image: Optional[ImageFile] = None
        self.current_photo: Optional[ImageTk.PhotoImage] = None
        self.original_image: Optional[Image.Image] = None
        self.scale_factor: float = 1.0
        self.zoom_level: float = 1.0
        self.image_size: Tuple[int, int] = (0, 0)
        self.image_position: Tuple[int, int] = (0, 0)
        self.callbacks = {}

        # Pan state
        self.pan_start_x = 0
        self.pan_start_y = 0
        self.is_panning = False
        
        # Drawing state
        self.drawing_mode = False
        self.current_drawing: Optional[DrawingElement] = None
        self.start_x = 0
        self.start_y = 0
        self.temp_rect = None
        
        self._create_widgets()
        
    def _create_widgets(self):
        """Create the canvas widgets."""
        # Clear parent frame
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Title and toolbar
        header_frame = ctk.CTkFrame(self.parent)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        title_label = ctk.CTkLabel(
            header_frame, 
            text="Image Canvas", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(side="left", padx=10, pady=5)
        
        # Toolbar buttons
        toolbar_frame = ctk.CTkFrame(header_frame)
        toolbar_frame.pack(side="right", padx=10, pady=5)
        
        self.draw_btn = ctk.CTkButton(
            toolbar_frame,
            text="Draw Mode",
            command=self._toggle_draw_mode,
            width=100,
            height=30
        )
        self.draw_btn.pack(side="left", padx=2)
        
        self.clear_btn = ctk.CTkButton(
            toolbar_frame,
            text="Clear All",
            command=self._clear_drawings,
            width=100,
            height=30
        )
        self.clear_btn.pack(side="left", padx=2)
        
        self.zoom_fit_btn = ctk.CTkButton(
            toolbar_frame,
            text="Fit to Window",
            command=self._fit_to_window,
            width=100,
            height=30
        )
        self.zoom_fit_btn.pack(side="left", padx=2)

        self.zoom_in_btn = ctk.CTkButton(
            toolbar_frame,
            text="Zoom In",
            command=self._zoom_in,
            width=80,
            height=30
        )
        self.zoom_in_btn.pack(side="left", padx=2)

        self.zoom_out_btn = ctk.CTkButton(
            toolbar_frame,
            text="Zoom Out",
            command=self._zoom_out,
            width=80,
            height=30
        )
        self.zoom_out_btn.pack(side="left", padx=2)

        # Zoom level label
        self.zoom_label = ctk.CTkLabel(
            toolbar_frame,
            text="100%",
            width=50
        )
        self.zoom_label.pack(side="left", padx=5)
        
        # Canvas frame
        canvas_frame = ctk.CTkFrame(self.parent)
        canvas_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Create canvas with scrollbars
        self.canvas = tk.Canvas(
            canvas_frame,
            bg=settings.canvas_bg_color,
            highlightthickness=0
        )
        
        # Scrollbars
        h_scrollbar = tk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        
        self.canvas.configure(
            xscrollcommand=h_scrollbar.set,
            yscrollcommand=v_scrollbar.set
        )
        
        # Pack canvas and scrollbars
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Configure grid weights
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Bind canvas events
        self.canvas.bind("<Button-1>", self._on_canvas_click)
        self.canvas.bind("<B1-Motion>", self._on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_canvas_release)
        self.canvas.bind("<Double-Button-1>", self._on_canvas_double_click)
        self.canvas.bind("<MouseWheel>", self._on_mouse_wheel)
        self.canvas.bind("<Button-4>", self._on_mouse_wheel)  # Linux
        self.canvas.bind("<Button-5>", self._on_mouse_wheel)  # Linux

        # Pan events (middle mouse button or Ctrl+drag)
        self.canvas.bind("<Button-2>", self._start_pan)  # Middle mouse button
        self.canvas.bind("<B2-Motion>", self._do_pan)
        self.canvas.bind("<ButtonRelease-2>", self._end_pan)

        # Ctrl+drag for panning
        self.canvas.bind("<Control-Button-1>", self._start_pan)
        self.canvas.bind("<Control-B1-Motion>", self._do_pan)
        self.canvas.bind("<Control-ButtonRelease-1>", self._end_pan)

        # Keyboard shortcuts
        self.canvas.bind("<Key>", self._on_key_press)
        self.canvas.focus_set()  # Allow canvas to receive keyboard events
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.parent,
            text="No image loaded",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=5)
        
        logger.info("Image canvas created")
    
    def register_callback(self, event: str, callback: Callable):
        """Register a callback for an event."""
        self.callbacks[event] = callback
    
    def _trigger_callback(self, event: str, *args, **kwargs):
        """Trigger a registered callback."""
        if event in self.callbacks:
            try:
                return self.callbacks[event](*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in callback {event}: {e}")
                messagebox.showerror("Error", f"An error occurred: {e}")
    
    def load_image(self, image_file: ImageFile):
        """Load an image into the canvas."""
        try:
            image_path = Path(image_file.path)
            if not image_path.exists():
                raise FileNotFoundError(f"Image file not found: {image_path}")

            # Load original image
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                self.original_image = img.copy()

            self.current_image = image_file
            self.image_size = (image_file.width, image_file.height)

            # Reset zoom and position
            self.zoom_level = 1.0
            self.image_position = (0, 0)

            # Initial fit to window
            self._fit_to_window()

            logger.info(f"Loaded image: {image_file.name}")

        except Exception as e:
            logger.error(f"Failed to load image {image_file.path}: {e}")
            messagebox.showerror("Error", f"Failed to load image: {e}")

    def _update_image_display(self):
        """Update the image display with current zoom and position."""
        if not self.original_image:
            return

        try:
            # Get canvas size
            self.canvas.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                return

            # Calculate display size
            display_width = int(self.image_size[0] * self.zoom_level)
            display_height = int(self.image_size[1] * self.zoom_level)

            # Resize image
            if display_width > 0 and display_height > 0:
                resized_image = self.original_image.resize(
                    (display_width, display_height),
                    Image.Resampling.LANCZOS
                )
                self.current_photo = ImageTk.PhotoImage(resized_image)
            else:
                return

            # Clear canvas and display image
            self.canvas.delete("all")

            # Position image
            x, y = self.image_position
            self.canvas.create_image(x, y, anchor="nw", image=self.current_photo, tags="image")

            # Update scroll region
            bbox = self.canvas.bbox("all")
            if bbox:
                self.canvas.configure(scrollregion=bbox)

            # Redraw existing drawings
            self._redraw_drawings()

            # Update status
            zoom_percent = int(self.zoom_level * 100)
            self.zoom_label.configure(text=f"{zoom_percent}%")
            self.status_label.configure(
                text=f"{self.current_image.name} - {self.current_image.width}x{self.current_image.height} - Zoom: {zoom_percent}%"
            )

        except Exception as e:
            logger.error(f"Failed to update image display: {e}")
    
    def _toggle_draw_mode(self):
        """Toggle drawing mode."""
        self.drawing_mode = not self.drawing_mode
        
        if self.drawing_mode:
            self.draw_btn.configure(text="Exit Draw")
            self.canvas.configure(cursor="crosshair")
        else:
            self.draw_btn.configure(text="Draw Mode")
            self.canvas.configure(cursor="")
            
            # Cancel current drawing
            if self.temp_rect:
                self.canvas.delete(self.temp_rect)
                self.temp_rect = None
        
        logger.debug(f"Drawing mode: {'ON' if self.drawing_mode else 'OFF'}")
    
    def _clear_drawings(self):
        """Clear all drawings from the current image."""
        if not self.current_image:
            return
        
        if self.current_image.drawings:
            result = messagebox.askyesno(
                "Clear Drawings",
                "Are you sure you want to clear all drawings for this image?"
            )
            
            if result:
                self.current_image.drawings.clear()
                self._redraw_drawings()
                self._trigger_callback('image_modified', self.current_image)
                logger.info("Cleared all drawings")
    
    def _fit_to_window(self):
        """Fit image to window size."""
        if not self.original_image:
            return

        # Get canvas size
        self.canvas.update_idletasks()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            return

        # Calculate zoom to fit
        zoom_x = canvas_width / self.image_size[0]
        zoom_y = canvas_height / self.image_size[1]
        self.zoom_level = min(zoom_x, zoom_y, 1.0)  # Don't zoom in beyond 100%

        # Center image
        display_width = int(self.image_size[0] * self.zoom_level)
        display_height = int(self.image_size[1] * self.zoom_level)

        self.image_position = (
            (canvas_width - display_width) // 2,
            (canvas_height - display_height) // 2
        )

        self._update_image_display()
        logger.debug("Fitted image to window")

    def _zoom_in(self):
        """Zoom in on the image."""
        if not self.original_image:
            return

        # Zoom in by 25%
        new_zoom = self.zoom_level * 1.25
        max_zoom = 10.0  # Maximum 1000% zoom

        if new_zoom <= max_zoom:
            self._zoom_to_level(new_zoom)

    def _zoom_out(self):
        """Zoom out on the image."""
        if not self.original_image:
            return

        # Zoom out by 25%
        new_zoom = self.zoom_level / 1.25
        min_zoom = 0.1  # Minimum 10% zoom

        if new_zoom >= min_zoom:
            self._zoom_to_level(new_zoom)

    def _zoom_to_level(self, zoom_level: float, center_point: Optional[Tuple[int, int]] = None):
        """Zoom to a specific level, optionally centering on a point."""
        if not self.original_image:
            return

        old_zoom = self.zoom_level
        self.zoom_level = zoom_level

        # If center point provided, adjust position to keep that point centered
        if center_point:
            canvas_x, canvas_y = center_point

            # Convert canvas coordinates to image coordinates
            old_img_x = (canvas_x - self.image_position[0]) / old_zoom
            old_img_y = (canvas_y - self.image_position[1]) / old_zoom

            # Calculate new position to keep the same image point under the cursor
            new_x = canvas_x - (old_img_x * self.zoom_level)
            new_y = canvas_y - (old_img_y * self.zoom_level)

            self.image_position = (int(new_x), int(new_y))

        self._update_image_display()
        logger.debug(f"Zoomed to {self.zoom_level:.2f}x")
    
    def _on_canvas_click(self, event):
        """Handle canvas click events."""
        # Check if this is a pan operation (Ctrl+click or middle mouse)
        if event.state & 0x4 or event.num == 2:  # Ctrl key or middle mouse
            return  # Pan handling is done in separate methods

        if not self.drawing_mode or not self.current_image:
            return

        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)

        # Start drawing rectangle
        self.temp_rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline=settings.selection_color,
            width=settings.selection_width,
            tags="temp_drawing"
        )
    
    def _on_canvas_drag(self, event):
        """Handle canvas drag events."""
        if not self.drawing_mode or not self.temp_rect:
            return
        
        current_x = self.canvas.canvasx(event.x)
        current_y = self.canvas.canvasy(event.y)
        
        # Update rectangle
        self.canvas.coords(self.temp_rect, self.start_x, self.start_y, current_x, current_y)
    
    def _on_canvas_release(self, event):
        """Handle canvas release events."""
        if not self.drawing_mode or not self.temp_rect or not self.current_image:
            return
        
        end_x = self.canvas.canvasx(event.x)
        end_y = self.canvas.canvasy(event.y)
        
        # Calculate rectangle bounds
        x1, y1 = min(self.start_x, end_x), min(self.start_y, end_y)
        x2, y2 = max(self.start_x, end_x), max(self.start_y, end_y)
        
        # Check minimum size
        if abs(x2 - x1) < 10 or abs(y2 - y1) < 10:
            self.canvas.delete(self.temp_rect)
            self.temp_rect = None
            return
        
        # Convert to image coordinates using current zoom and position
        img_x1 = (x1 - self.image_position[0]) / self.zoom_level
        img_y1 = (y1 - self.image_position[1]) / self.zoom_level
        img_x2 = (x2 - self.image_position[0]) / self.zoom_level
        img_y2 = (y2 - self.image_position[1]) / self.zoom_level

        # Clamp to image bounds
        img_x1 = max(0, min(img_x1, self.image_size[0]))
        img_y1 = max(0, min(img_y1, self.image_size[1]))
        img_x2 = max(0, min(img_x2, self.image_size[0]))
        img_y2 = max(0, min(img_y2, self.image_size[1]))
        
        # Create drawing element
        drawing = DrawingElement(
            type="rectangle",
            points=[(img_x1, img_y1), (img_x2, img_y2)],
            color=settings.selection_color,
            width=settings.selection_width
        )
        
        # Add to image
        self.current_image.add_drawing(drawing)
        
        # Remove temporary rectangle and redraw
        self.canvas.delete(self.temp_rect)
        self.temp_rect = None
        self._redraw_drawings()
        
        # Notify of change
        self._trigger_callback('image_modified', self.current_image)
        self._trigger_callback('drawing_created', drawing)
        
        logger.info(f"Created drawing: {drawing.id}")
    
    def _on_canvas_double_click(self, event):
        """Handle canvas double-click events."""
        # Find clicked drawing and trigger transcription
        if not self.current_image:
            return
        
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # Convert to image coordinates using current zoom and position
        img_x = (canvas_x - self.image_position[0]) / self.zoom_level
        img_y = (canvas_y - self.image_position[1]) / self.zoom_level
        
        # Find drawing at this point
        for drawing in self.current_image.drawings:
            if drawing.type == "rectangle" and len(drawing.points) >= 2:
                x1, y1 = drawing.points[0]
                x2, y2 = drawing.points[1]
                
                if min(x1, x2) <= img_x <= max(x1, x2) and min(y1, y2) <= img_y <= max(y1, y2):
                    # Create bounding box and trigger OCR
                    bbox = BoundingBox(
                        x=min(x1, x2),
                        y=min(y1, y2),
                        width=abs(x2 - x1),
                        height=abs(y2 - y1)
                    )
                    self._trigger_callback('generate_transcription', self.current_image, bbox)
                    break
    
    def _on_mouse_wheel(self, event):
        """Handle mouse wheel events for zooming."""
        if not self.original_image:
            return

        # Get mouse position
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Determine zoom direction
        if event.delta > 0 or event.num == 4:  # Zoom in
            new_zoom = self.zoom_level * 1.1
            max_zoom = 10.0
            if new_zoom <= max_zoom:
                self._zoom_to_level(new_zoom, (canvas_x, canvas_y))
        elif event.delta < 0 or event.num == 5:  # Zoom out
            new_zoom = self.zoom_level / 1.1
            min_zoom = 0.1
            if new_zoom >= min_zoom:
                self._zoom_to_level(new_zoom, (canvas_x, canvas_y))

    def _start_pan(self, event):
        """Start panning operation."""
        self.is_panning = True
        self.pan_start_x = event.x
        self.pan_start_y = event.y
        self.canvas.configure(cursor="fleur")  # Hand cursor

    def _do_pan(self, event):
        """Perform panning operation."""
        if not self.is_panning:
            return

        # Calculate pan delta
        dx = event.x - self.pan_start_x
        dy = event.y - self.pan_start_y

        # Update image position
        new_x = self.image_position[0] + dx
        new_y = self.image_position[1] + dy
        self.image_position = (new_x, new_y)

        # Update display
        self._update_image_display()

        # Update pan start position
        self.pan_start_x = event.x
        self.pan_start_y = event.y

    def _end_pan(self, event):
        """End panning operation."""
        self.is_panning = False
        self.canvas.configure(cursor="")
        # event parameter is required by tkinter binding but not used

    def _on_key_press(self, event):
        """Handle keyboard shortcuts."""
        if not self.original_image:
            return

        if event.keysym == "plus" or event.keysym == "equal":
            self._zoom_in()
        elif event.keysym == "minus":
            self._zoom_out()
        elif event.keysym == "0":
            self._fit_to_window()
        elif event.keysym == "1":
            # Zoom to 100%
            self._zoom_to_level(1.0)
        elif event.keysym == "f":
            self._fit_to_window()
    
    def _redraw_drawings(self):
        """Redraw all drawings for the current image."""
        if not self.current_image:
            return

        # Remove existing drawings
        self.canvas.delete("drawing")

        for drawing in self.current_image.drawings:
            if drawing.type == "rectangle" and len(drawing.points) >= 2:
                # Convert image coordinates to canvas coordinates
                img_x1, img_y1 = drawing.points[0]
                img_x2, img_y2 = drawing.points[1]

                # Scale to current zoom level and add image position offset
                canvas_x1 = img_x1 * self.zoom_level + self.image_position[0]
                canvas_y1 = img_y1 * self.zoom_level + self.image_position[1]
                canvas_x2 = img_x2 * self.zoom_level + self.image_position[0]
                canvas_y2 = img_y2 * self.zoom_level + self.image_position[1]

                self.canvas.create_rectangle(
                    canvas_x1, canvas_y1, canvas_x2, canvas_y2,
                    outline=drawing.color,
                    width=drawing.width,
                    tags="drawing"
                )
    
    def get_current_image(self) -> Optional[ImageFile]:
        """Get the currently loaded image."""
        return self.current_image
