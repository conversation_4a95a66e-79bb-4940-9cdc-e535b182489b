"""Application configuration settings."""

from pathlib import Path
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
import os


class AppSettings(BaseSettings):
    """Main application settings."""
    
    # Application
    app_name: str = "Image Transcriber"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Paths
    base_dir: Path = Path(__file__).parent.parent.parent
    projects_dir: Path = base_dir / "projects"
    logs_dir: Path = base_dir / "logs"
    temp_dir: Path = base_dir / "temp"
    
    # GUI Settings
    window_width: int = 1400
    window_height: int = 900
    min_window_width: int = 1000
    min_window_height: int = 600
    
    # Theme
    appearance_mode: str = "dark"  # "light", "dark", "system"
    color_theme: str = "blue"  # "blue", "green", "dark-blue"
    
    # OCR Settings
    default_ocr_engine: str = "tesseract"
    tesseract_cmd: Optional[str] = None
    tesseract_languages: List[str] = ["eng"]
    easyocr_languages: List[str] = ["en"]
    paddleocr_language: str = "en"
    
    # Image Settings
    max_image_size: int = 4096  # Maximum dimension for display
    supported_formats: List[str] = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
    
    # Canvas Settings
    canvas_bg_color: str = "#2b2b2b"
    selection_color: str = "#1f538d"
    selection_width: int = 2
    
    # Logging
    log_level: str = "INFO"
    log_rotation: str = "10 MB"
    log_retention: str = "30 days"
    
    class Config:
        env_file = ".env"
        env_prefix = "TRANSCRIBER_"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
        self._setup_tesseract()
    
    def _ensure_directories(self):
        """Create necessary directories if they don't exist."""
        for directory in [self.projects_dir, self.logs_dir, self.temp_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _setup_tesseract(self):
        """Setup Tesseract command path if not specified."""
        if self.tesseract_cmd is None:
            # Try common Tesseract installation paths
            common_paths = [
                "/usr/bin/tesseract",
                "/usr/local/bin/tesseract",
                "/opt/homebrew/bin/tesseract",
                "C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
                "C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    self.tesseract_cmd = path
                    break


# Global settings instance
settings = AppSettings()
